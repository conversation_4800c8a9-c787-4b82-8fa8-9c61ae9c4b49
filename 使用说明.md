# 工地周边数据处理工具 - 使用说明

## 🎉 打包完成！

您的程序已经成功打包为独立的可执行文件！

### 📁 文件位置
- **可执行文件**: `dist/工地周边数据处理工具.exe`
- **文件大小**: 35.38 MB
- **图标**: 使用了 icon.ico 作为程序图标

### 🚀 使用方法

#### 1. 直接运行
双击 `dist/工地周边数据处理工具.exe` 即可运行程序

#### 2. 准备数据文件
确保您有以下数据文件：
- **走航数据文件** (CSV格式): 包含纬度、经度等字段
- **工地台账文件** (Excel格式): 包含工地名称、纬度、经度等字段

#### 3. 程序操作步骤
1. 启动程序后，点击"浏览..."按钮选择走航数据文件
2. 点击"浏览..."按钮选择工地台账文件
3. 设置搜索半径（默认500米）
4. 点击"开始处理"按钮
5. 选择保存位置，程序会自动匹配数据并保存结果

### 💡 特点
- ✅ **独立运行**: 无需安装Python环境
- ✅ **便携性**: 可复制到其他Windows电脑直接使用
- ✅ **GUI界面**: 友好的图形用户界面
- ✅ **多进程处理**: 支持大数据集快速处理
- ✅ **自定义半径**: 可调整搜索半径范围

### 📋 系统要求
- **操作系统**: Windows 7/8/10/11
- **内存**: 建议4GB以上
- **磁盘空间**: 至少100MB可用空间

### 🔧 开发环境信息
- **Python版本**: 3.13.2
- **PyInstaller版本**: 6.14.2
- **主要依赖**:
  - pandas 2.3.1 (数据处理)
  - numpy 2.3.1 (数值计算)
  - geopy 2.4.1 (地理距离计算)
  - openpyxl 3.1.5 (Excel文件处理)

### 📂 项目结构
```
方便截图软件/
├── dist/
│   └── 工地周边数据处理工具.exe    # 可执行文件
├── data/
│   ├── 工地台账.xlsx              # 示例工地台账
│   └── 走航数据.csv               # 示例走航数据
├── venv/                          # 虚拟环境
├── build/                         # 打包临时文件
├── icon.ico                       # 程序图标
├── main.py                        # 源代码
├── requirements.txt               # 依赖列表
└── 工地周边数据处理工具.spec       # PyInstaller配置
```

### ⚠️ 注意事项
1. **首次启动**: 可能需要几秒钟加载时间
2. **杀毒软件**: 如果被误报为病毒，请添加到白名单
3. **数据格式**: 确保数据文件格式正确，包含必要的字段
4. **文件权限**: 确保程序有读写文件的权限

### 🐛 常见问题
1. **程序无法启动**: 检查是否被杀毒软件阻止
2. **处理速度慢**: 大数据集处理需要时间，请耐心等待
3. **内存不足**: 处理大文件时可能需要更多内存

### 📞 技术支持
如遇到问题，请检查：
- 数据文件格式是否正确
- 系统资源是否充足
- 文件路径是否包含特殊字符

---

🎊 **恭喜！您的程序已成功打包并可以独立运行！**
