# -*- mode: python ; coding: utf-8 -*-
"""
工地周边数据处理工具 - PyInstaller配置文件
优化版本 v2.0.0

此配置文件用于将Python应用程序打包为独立的可执行文件
包含了优化的设置以确保程序正常运行和最佳性能
"""

import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(os.getcwd())

# 数据文件配置
datas = [
    # 包含图标文件
    (str(project_root / 'icon.ico'), '.'),
    # 如果有其他资源文件，在这里添加
    # (str(project_root / 'data'), 'data'),
]

# 隐藏导入模块（解决某些模块无法自动检测的问题）
hiddenimports = [
    # 修复http模块问题
    'http',
    'http.client',
    'http.server',
    'http.cookies',
    'urllib',
    'urllib.request',
    'urllib.parse',
    'urllib.error',

    # 数据处理相关
    'pandas._libs.tslibs.timedeltas',
    'pandas._libs.tslibs.np_datetime',
    'pandas._libs.tslibs.nattype',
    'pandas._libs.properties',
    'pandas._libs.testing',

    # 地理计算相关
    'geopy.distance',
    'geopy.geocoders',

    # Excel处理相关
    'openpyxl.cell._writer',
    'openpyxl.workbook.external_link.external',

    # 多进程相关
    'multiprocessing.pool',
    'multiprocessing.util',

    # GUI相关
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',

    # 系统相关
    'ctypes.wintypes',

    # 包资源相关
    'pkg_resources',
    'pkg_resources._vendor',
    'pkg_resources.extern',

    # 其他可能缺失的模块
    'email',
    'email.mime',
    'email.mime.text',
    'json',
    'xml',
    'xml.etree',
    'xml.etree.ElementTree',
]

# 排除的模块（减少打包大小）
excludes = [
    # 测试相关
    'pytest',
    'unittest',
    'test',

    # 开发工具
    'IPython',
    'jupyter',
    'notebook',

    # 不需要的GUI框架
    'PyQt5',
    'PyQt6',
    'PySide2',
    'PySide6',
    'wx',

    # 不需要的科学计算库
    'matplotlib',
    'scipy',
    'sklearn',
    'tensorflow',
    'torch',

    # 不需要的网络库（注意：不排除http和urllib，因为可能被依赖）
    'requests',
    'urllib3',

    # 其他不需要的模块
    'doctest',
    'pdb',
    'profile',
    'pstats',
]

a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=2,  # 启用字节码优化
    cipher=None,  # 可以添加加密
)

# 移除重复的模块
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='工地周边数据处理工具_v2.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[
        # 排除某些文件的UPX压缩（避免兼容性问题）
        'vcruntime140.dll',
        'msvcp140.dll',
        'api-ms-win-*.dll',
    ],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'icon.ico'),
    version_file=None,  # 可以添加版本信息文件
    uac_admin=False,  # 不需要管理员权限
    uac_uiaccess=False,
    manifest=None,
)
