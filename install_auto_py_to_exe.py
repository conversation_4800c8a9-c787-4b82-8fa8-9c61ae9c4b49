#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装和启动 auto-py-to-exe 图形化打包工具
这是最简单的Python程序打包方式
"""

import subprocess
import sys
import os

def install_auto_py_to_exe():
    """安装 auto-py-to-exe"""
    print("正在安装 auto-py-to-exe...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'auto-py-to-exe'], check=True)
        print("✅ auto-py-to-exe 安装成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def launch_auto_py_to_exe():
    """启动 auto-py-to-exe"""
    print("正在启动 auto-py-to-exe...")
    try:
        subprocess.run([sys.executable, '-m', 'auto_py_to_exe'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")

def main():
    print("🚀 Auto-py-to-exe 安装和启动工具")
    print("=" * 50)
    
    # 检查是否已安装
    try:
        import auto_py_to_exe
        print("✅ auto-py-to-exe 已安装")
    except ImportError:
        print("📦 正在安装 auto-py-to-exe...")
        if not install_auto_py_to_exe():
            return
    
    print("\n🎯 使用说明：")
    print("1. 在打开的网页界面中，选择 main.py 作为脚本文件")
    print("2. 选择 'One File' 模式")
    print("3. 选择 'Window Based' (不显示控制台)")
    print("4. 在 'Additional Files' 中添加 icon.ico")
    print("5. 点击 'CONVERT .PY TO .EXE' 开始打包")
    print("\n正在启动图形界面...")
    
    launch_auto_py_to_exe()

if __name__ == '__main__':
    main()
