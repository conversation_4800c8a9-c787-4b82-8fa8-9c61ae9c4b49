('E:\\cursor_project\\方便截图软件\\build\\工地周边数据处理工具\\PYZ-00.pyz',
 [('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE-2'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE-2'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE-2'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE-2'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE-2'),
  ('_distutils_hack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE-2'),
  ('_distutils_hack.override',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE-2'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE-2'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE-2'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE-2'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE-2'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE-2'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE-2'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE-2'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE-2'),
  ('_pyrepl.base_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE-2'),
  ('_pyrepl.commands',
   'C:\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE-2'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE-2'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE-2'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE-2'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE-2'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE-2'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE-2'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE-2'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE-2'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE-2'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE-2'),
  ('_pyrepl.readline',
   'C:\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE-2'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE-2'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE-2'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE-2'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE-2'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE-2'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE-2'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE-2'),
  ('_pyrepl.windows_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE-2'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE-2'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE-2'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE-2'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE-2'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE-2'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE-2'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE-2'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE-2'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE-2'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE-2'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE-2'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE-2'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE-2'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'C:\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE-2'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE-2'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE-2'),
  ('asyncio.timeouts',
   'C:\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE-2'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('backports',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE-2'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE-2'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE-2'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE-2'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE-2'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE-2'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE-2'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE-2'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE-2'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE-2'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE-2'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE-2'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE-2'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE-2'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE-2'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE-2'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-2'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-2'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE-2'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE-2'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE-2'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE-2'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE-2'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE-2'),
  ('dateutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-2'),
  ('dateutil._common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-2'),
  ('dateutil._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-2'),
  ('dateutil.easter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-2'),
  ('dateutil.parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.parser._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-2'),
  ('dateutil.parser.isoparser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-2'),
  ('dateutil.relativedelta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-2'),
  ('dateutil.rrule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-2'),
  ('dateutil.tz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.tz._common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-2'),
  ('dateutil.tz._factories',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-2'),
  ('dateutil.tz.tz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-2'),
  ('dateutil.tz.win',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-2'),
  ('dateutil.zoneinfo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-2'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE-2'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE-2'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE-2'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE-2'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'C:\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'C:\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE-2'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE-2'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE-2'),
  ('email.feedparser',
   'C:\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE-2'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE-2'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE-2'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE-2'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE-2'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE-2'),
  ('email.quoprimime',
   'C:\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE-2'),
  ('et_xmlfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-2'),
  ('et_xmlfile.incremental_tree',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-2'),
  ('et_xmlfile.xmlfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-2'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE-2'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE-2'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE-2'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE-2'),
  ('geographiclib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\__init__.py',
   'PYMODULE-2'),
  ('geographiclib.accumulator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\accumulator.py',
   'PYMODULE-2'),
  ('geographiclib.constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\constants.py',
   'PYMODULE-2'),
  ('geographiclib.geodesic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesic.py',
   'PYMODULE-2'),
  ('geographiclib.geodesiccapability',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesiccapability.py',
   'PYMODULE-2'),
  ('geographiclib.geodesicline',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesicline.py',
   'PYMODULE-2'),
  ('geographiclib.geomath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geomath.py',
   'PYMODULE-2'),
  ('geographiclib.polygonarea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\polygonarea.py',
   'PYMODULE-2'),
  ('geopy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\__init__.py',
   'PYMODULE-2'),
  ('geopy.adapters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\adapters.py',
   'PYMODULE-2'),
  ('geopy.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\compat.py',
   'PYMODULE-2'),
  ('geopy.distance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\distance.py',
   'PYMODULE-2'),
  ('geopy.exc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\exc.py',
   'PYMODULE-2'),
  ('geopy.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\format.py',
   'PYMODULE-2'),
  ('geopy.geocoders',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\__init__.py',
   'PYMODULE-2'),
  ('geopy.geocoders.arcgis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\arcgis.py',
   'PYMODULE-2'),
  ('geopy.geocoders.azure',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\azure.py',
   'PYMODULE-2'),
  ('geopy.geocoders.baidu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\baidu.py',
   'PYMODULE-2'),
  ('geopy.geocoders.banfrance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\banfrance.py',
   'PYMODULE-2'),
  ('geopy.geocoders.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\base.py',
   'PYMODULE-2'),
  ('geopy.geocoders.bing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\bing.py',
   'PYMODULE-2'),
  ('geopy.geocoders.databc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\databc.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geocodeearth',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geocodeearth.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geocodio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geocodio.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geokeo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geokeo.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geolake',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geolake.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geonames',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geonames.py',
   'PYMODULE-2'),
  ('geopy.geocoders.google',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\google.py',
   'PYMODULE-2'),
  ('geopy.geocoders.here',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\here.py',
   'PYMODULE-2'),
  ('geopy.geocoders.ignfrance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\ignfrance.py',
   'PYMODULE-2'),
  ('geopy.geocoders.mapbox',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\mapbox.py',
   'PYMODULE-2'),
  ('geopy.geocoders.mapquest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\mapquest.py',
   'PYMODULE-2'),
  ('geopy.geocoders.maptiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\maptiler.py',
   'PYMODULE-2'),
  ('geopy.geocoders.nominatim',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\nominatim.py',
   'PYMODULE-2'),
  ('geopy.geocoders.opencage',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\opencage.py',
   'PYMODULE-2'),
  ('geopy.geocoders.openmapquest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\openmapquest.py',
   'PYMODULE-2'),
  ('geopy.geocoders.pelias',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\pelias.py',
   'PYMODULE-2'),
  ('geopy.geocoders.photon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\photon.py',
   'PYMODULE-2'),
  ('geopy.geocoders.pickpoint',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\pickpoint.py',
   'PYMODULE-2'),
  ('geopy.geocoders.smartystreets',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\smartystreets.py',
   'PYMODULE-2'),
  ('geopy.geocoders.tomtom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\tomtom.py',
   'PYMODULE-2'),
  ('geopy.geocoders.what3words',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\what3words.py',
   'PYMODULE-2'),
  ('geopy.geocoders.woosmap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\woosmap.py',
   'PYMODULE-2'),
  ('geopy.geocoders.yandex',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\yandex.py',
   'PYMODULE-2'),
  ('geopy.location',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\location.py',
   'PYMODULE-2'),
  ('geopy.point',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\point.py',
   'PYMODULE-2'),
  ('geopy.timezone',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\timezone.py',
   'PYMODULE-2'),
  ('geopy.units',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\units.py',
   'PYMODULE-2'),
  ('geopy.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\util.py',
   'PYMODULE-2'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE-2'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE-2'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE-2'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE-2'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE-2'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE-2'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE-2'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE-2'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE-2'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE-2'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE-2'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE-2'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE-2'),
  ('jaraco', '-', 'PYMODULE-2'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE-2'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE-2'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE-2'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE-2'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE-2'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE-2'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE-2'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE-2'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE-2'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE-2'),
  ('numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy._core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy.char',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib._ctypeslib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.f2py',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py.__version__',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._backend',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._distutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._meson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-2'),
  ('numpy.f2py._isocbind',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-2'),
  ('numpy.f2py.auxfuncs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.capi_maps',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-2'),
  ('numpy.f2py.cb_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.cfuncs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.common_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.crackfortran',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-2'),
  ('numpy.f2py.diagnose',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-2'),
  ('numpy.f2py.f2py2e',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-2'),
  ('numpy.f2py.f90mod_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.func2subr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-2'),
  ('numpy.f2py.rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.symbolic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-2'),
  ('numpy.f2py.use_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib._format_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.recfunctions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private.extbuild',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-2'),
  ('numpy.testing._private.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-2'),
  ('numpy.testing.overrides',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE-2'),
  ('openpyxl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl._constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-2'),
  ('openpyxl.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.cell._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.cell.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.cell.read_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-2'),
  ('openpyxl.cell.rich_text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-2'),
  ('openpyxl.cell.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chart._3d',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-2'),
  ('openpyxl.chart._chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.area_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.axis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bar_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bubble_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.chartspace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-2'),
  ('openpyxl.chart.data_source',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-2'),
  ('openpyxl.chart.descriptors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-2'),
  ('openpyxl.chart.error_bar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-2'),
  ('openpyxl.chart.label',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-2'),
  ('openpyxl.chart.layout',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-2'),
  ('openpyxl.chart.legend',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-2'),
  ('openpyxl.chart.line_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.marker',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-2'),
  ('openpyxl.chart.picture',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pie_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-2'),
  ('openpyxl.chart.plotarea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-2'),
  ('openpyxl.chart.print_settings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.radar_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-2'),
  ('openpyxl.chart.scatter_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series_factory',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-2'),
  ('openpyxl.chart.shapes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-2'),
  ('openpyxl.chart.stock_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.surface_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart.title',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-2'),
  ('openpyxl.chart.trendline',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-2'),
  ('openpyxl.chart.updown_bars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.chartsheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.custom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.publish',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.relation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.comments',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.comments.author',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comment_sheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comments',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-2'),
  ('openpyxl.comments.shape_writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-2'),
  ('openpyxl.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.compat.numbers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.compat.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.container',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.namespace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.nested',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.sequence',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.serialisable',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-2'),
  ('openpyxl.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.colors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.connector',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.effect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.fill',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.geometry',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.graphic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.image',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.line',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.picture',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.relation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.xdr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-2'),
  ('openpyxl.formatting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.formatting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.rule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-2'),
  ('openpyxl.formula',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formula.tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-2'),
  ('openpyxl.formula.translate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-2'),
  ('openpyxl.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.custom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.extended',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.manifest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.relationship',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.cache',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.fields',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.record',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-2'),
  ('openpyxl.reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.reader.drawings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-2'),
  ('openpyxl.reader.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.reader.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.reader.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.styles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.styles.alignment',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-2'),
  ('openpyxl.styles.borders',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-2'),
  ('openpyxl.styles.builtins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-2'),
  ('openpyxl.styles.cell_style',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-2'),
  ('openpyxl.styles.colors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.styles.differential',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fills',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fonts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-2'),
  ('openpyxl.styles.named_styles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-2'),
  ('openpyxl.styles.numbers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.styles.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.styles.proxy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-2'),
  ('openpyxl.styles.styleable',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-2'),
  ('openpyxl.styles.stylesheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-2'),
  ('openpyxl.styles.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-2'),
  ('openpyxl.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.utils.bound_dictionary',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-2'),
  ('openpyxl.utils.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.utils.datetime',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-2'),
  ('openpyxl.utils.escape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-2'),
  ('openpyxl.utils.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-2'),
  ('openpyxl.utils.formulas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-2'),
  ('openpyxl.utils.indexed_list',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-2'),
  ('openpyxl.utils.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.utils.units',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-2'),
  ('openpyxl.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.child',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.defined_name',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link.external',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_reference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.function_group',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.smart_tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.web',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._read_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._write_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.cell_range',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.copier',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.datavalidation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.dimensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.filters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.formula',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.header_footer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.hyperlink',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.merge',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.page',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.pagebreak',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.print_settings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.related',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.scenario',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.worksheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-2'),
  ('openpyxl.writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.writer.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.writer.theme',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-2'),
  ('openpyxl.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.xml.constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-2'),
  ('openpyxl.xml.functions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-2'),
  ('packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE-2'),
  ('packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE-2'),
  ('packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE-2'),
  ('packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE-2'),
  ('packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE-2'),
  ('packaging.licenses',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE-2'),
  ('packaging.licenses._spdx',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE-2'),
  ('packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE-2'),
  ('packaging.metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE-2'),
  ('packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE-2'),
  ('packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE-2'),
  ('packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE-2'),
  ('packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE-2'),
  ('packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE-2'),
  ('pandas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config.config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-2'),
  ('pandas._config.dates',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-2'),
  ('pandas._config.display',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-2'),
  ('pandas._config.localization',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-2'),
  ('pandas._libs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.tslibs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.window',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing._io',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-2'),
  ('pandas._testing._warnings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-2'),
  ('pandas._testing.asserters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-2'),
  ('pandas._testing.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-2'),
  ('pandas._testing.contexts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-2'),
  ('pandas._typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-2'),
  ('pandas._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-2'),
  ('pandas._version_meson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-2'),
  ('pandas.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.indexers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.interchange',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-2'),
  ('pandas.arrays',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat._constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-2'),
  ('pandas.compat._optional',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-2'),
  ('pandas.compat.compressors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy.function',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-2'),
  ('pandas.compat.pickle_compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-2'),
  ('pandas.compat.pyarrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-2'),
  ('pandas.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.executor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-2'),
  ('pandas.core._numba.extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.mean_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.min_max_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.shared',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.sum_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.var_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-2'),
  ('pandas.core.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.algorithms',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-2'),
  ('pandas.core.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-2'),
  ('pandas.core.apply',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_accumulations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_reductions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.putmask',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.quantile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.replace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.take',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.transforms',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-2'),
  ('pandas.core.arraylike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-2'),
  ('pandas.core.arrays',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._arrow_string_mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._ranges',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.accessors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.extension_types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.boolean',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.categorical',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimelike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.floating',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.integer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.interval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.masked',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numpy_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.period',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_arrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-2'),
  ('pandas.core.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-2'),
  ('pandas.core.computation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.computation.align',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-2'),
  ('pandas.core.computation.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-2'),
  ('pandas.core.computation.check',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-2'),
  ('pandas.core.computation.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-2'),
  ('pandas.core.computation.engines',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-2'),
  ('pandas.core.computation.eval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expressions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-2'),
  ('pandas.core.computation.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.computation.parsing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-2'),
  ('pandas.core.computation.pytables',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-2'),
  ('pandas.core.computation.scope',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-2'),
  ('pandas.core.config_init',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-2'),
  ('pandas.core.construction',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.astype',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.cast',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.inference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.flags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-2'),
  ('pandas.core.frame',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-2'),
  ('pandas.core.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.groupby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.categorical',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.groupby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.grouper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.indexing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.indexers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.objects',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.indexes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.accessors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.category',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimelike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.frozen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.interval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.multi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.period',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.range',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.indexing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.interchange',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.buffer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.column',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe_protocol',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.from_dataframe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.internals',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.internals.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-2'),
  ('pandas.core.internals.array_manager',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-2'),
  ('pandas.core.internals.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-2'),
  ('pandas.core.internals.blocks',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-2'),
  ('pandas.core.internals.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.internals.construction',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.internals.managers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-2'),
  ('pandas.core.internals.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.methods',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.methods.describe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-2'),
  ('pandas.core.methods.selectn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-2'),
  ('pandas.core.methods.to_dict',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-2'),
  ('pandas.core.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.nanops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-2'),
  ('pandas.core.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.ops.array_ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-2'),
  ('pandas.core.ops.dispatch',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-2'),
  ('pandas.core.ops.docstrings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-2'),
  ('pandas.core.ops.invalid',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-2'),
  ('pandas.core.ops.mask_ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.resample',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-2'),
  ('pandas.core.reshape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.encoding',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.melt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.merge',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.reshape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.tile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-2'),
  ('pandas.core.roperator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-2'),
  ('pandas.core.sample',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-2'),
  ('pandas.core.series',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-2'),
  ('pandas.core.shared_docs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-2'),
  ('pandas.core.sorting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-2'),
  ('pandas.core.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.strings.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.strings.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-2'),
  ('pandas.core.strings.object_array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-2'),
  ('pandas.core.tools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.tools.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.tools.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.tools.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.tools.times',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-2'),
  ('pandas.core.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.util.hashing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-2'),
  ('pandas.core.util.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.window.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-2'),
  ('pandas.core.window.doc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-2'),
  ('pandas.core.window.ewm',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-2'),
  ('pandas.core.window.expanding',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-2'),
  ('pandas.core.window.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window.online',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-2'),
  ('pandas.core.window.rolling',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-2'),
  ('pandas.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io._util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-2'),
  ('pandas.io.clipboard',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.clipboards',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-2'),
  ('pandas.io.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-2'),
  ('pandas.io.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.excel._base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-2'),
  ('pandas.io.excel._calamine',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odfreader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odswriter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._openpyxl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-2'),
  ('pandas.io.excel._pyxlsb',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-2'),
  ('pandas.io.excel._util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlrd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlsxwriter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-2'),
  ('pandas.io.feather_format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-2'),
  ('pandas.io.formats',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats._color_data',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-2'),
  ('pandas.io.formats.console',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-2'),
  ('pandas.io.formats.css',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-2'),
  ('pandas.io.formats.csvs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-2'),
  ('pandas.io.formats.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-2'),
  ('pandas.io.formats.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-2'),
  ('pandas.io.formats.html',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-2'),
  ('pandas.io.formats.info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-2'),
  ('pandas.io.formats.printing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-2'),
  ('pandas.io.formats.string',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style_render',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-2'),
  ('pandas.io.formats.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-2'),
  ('pandas.io.gbq',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-2'),
  ('pandas.io.html',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-2'),
  ('pandas.io.json',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.json._json',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-2'),
  ('pandas.io.json._normalize',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-2'),
  ('pandas.io.json._table_schema',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-2'),
  ('pandas.io.orc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-2'),
  ('pandas.io.parquet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-2'),
  ('pandas.io.parsers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.base_parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.c_parser_wrapper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.python_parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.readers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-2'),
  ('pandas.io.pickle',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-2'),
  ('pandas.io.pytables',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-2'),
  ('pandas.io.sas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas7bdat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_xport',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sasreader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-2'),
  ('pandas.io.spss',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-2'),
  ('pandas.io.sql',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-2'),
  ('pandas.io.stata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-2'),
  ('pandas.io.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-2'),
  ('pandas.plotting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-2'),
  ('pandas.plotting._core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-2'),
  ('pandas.plotting._misc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-2'),
  ('pandas.testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-2'),
  ('pandas.tseries',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-2'),
  ('pandas.tseries.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-2'),
  ('pandas.tseries.frequencies',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-2'),
  ('pandas.tseries.holiday',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-2'),
  ('pandas.tseries.offsets',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-2'),
  ('pandas.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.util._decorators',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-2'),
  ('pandas.util._exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-2'),
  ('pandas.util._print_versions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-2'),
  ('pandas.util._tester',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-2'),
  ('pandas.util._validators',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-2'),
  ('pandas.util.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-2'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE-2'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE-2'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE-2'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE-2'),
  ('pickletools', 'C:\\Python313\\Lib\\pickletools.py', 'PYMODULE-2'),
  ('pkg_resources',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE-2'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE-2'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE-2'),
  ('plistlib', 'C:\\Python313\\Lib\\plistlib.py', 'PYMODULE-2'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE-2'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE-2'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE-2'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE-2'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pytz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-2'),
  ('pytz.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-2'),
  ('pytz.lazy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-2'),
  ('pytz.tzfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-2'),
  ('pytz.tzinfo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-2'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE-2'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE-2'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE-2'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE-2'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE-2'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE-2'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE-2'),
  ('setuptools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._core_metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE-2'),
  ('setuptools._distutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils._log',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE-2'),
  ('setuptools._distutils._modified',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE-2'),
  ('setuptools._distutils._msvccompiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE-2'),
  ('setuptools._distutils.archive_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.ccompiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE-2'),
  ('setuptools._distutils.cmd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.bdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.build',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.build_ext',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.sdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compat.numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE-2'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE-2'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE-2'),
  ('setuptools._distutils.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE-2'),
  ('setuptools._distutils.debug',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE-2'),
  ('setuptools._distutils.dir_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.dist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE-2'),
  ('setuptools._distutils.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE-2'),
  ('setuptools._distutils.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE-2'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE-2'),
  ('setuptools._distutils.file_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.filelist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE-2'),
  ('setuptools._distutils.log',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE-2'),
  ('setuptools._distutils.spawn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE-2'),
  ('setuptools._distutils.sysconfig',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE-2'),
  ('setuptools._distutils.text_file',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE-2'),
  ('setuptools._distutils.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE-2'),
  ('setuptools._distutils.versionpredicate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE-2'),
  ('setuptools._entry_points',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE-2'),
  ('setuptools._imp',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE-2'),
  ('setuptools._importlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE-2'),
  ('setuptools._itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE-2'),
  ('setuptools._normalization',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE-2'),
  ('setuptools._path',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE-2'),
  ('setuptools._reqs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE-2'),
  ('setuptools._shutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE-2'),
  ('setuptools._static',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE-2'),
  ('setuptools._vendor', '-', 'PYMODULE-2'),
  ('setuptools._vendor.backports',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE-2'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE-2'),
  ('setuptools._vendor.jaraco.context',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE-2'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.jaraco.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.more_itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE-2'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.android',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.macos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.unix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.windows',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli._re',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli._types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE-2'),
  ('setuptools._vendor.typing_extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp.glob',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE-2'),
  ('setuptools.archive_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE-2'),
  ('setuptools.command',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.command._requirestxt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE-2'),
  ('setuptools.command.bdist_egg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE-2'),
  ('setuptools.command.bdist_wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE-2'),
  ('setuptools.command.build',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE-2'),
  ('setuptools.command.egg_info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE-2'),
  ('setuptools.command.sdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE-2'),
  ('setuptools.command.setopt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE-2'),
  ('setuptools.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.compat.py310',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE-2'),
  ('setuptools.compat.py311',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE-2'),
  ('setuptools.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE-2'),
  ('setuptools.config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE-2'),
  ('setuptools.config.expand',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE-2'),
  ('setuptools.config.pyprojecttoml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE-2'),
  ('setuptools.config.setupcfg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE-2'),
  ('setuptools.depends',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE-2'),
  ('setuptools.discovery',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE-2'),
  ('setuptools.dist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE-2'),
  ('setuptools.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE-2'),
  ('setuptools.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE-2'),
  ('setuptools.glob',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE-2'),
  ('setuptools.installer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE-2'),
  ('setuptools.logging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE-2'),
  ('setuptools.monkey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE-2'),
  ('setuptools.msvc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE-2'),
  ('setuptools.unicode_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE-2'),
  ('setuptools.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE-2'),
  ('setuptools.warnings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE-2'),
  ('setuptools.wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE-2'),
  ('setuptools.windows_support',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE-2'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE-2'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE-2'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE-2'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE-2'),
  ('six',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\six.py',
   'PYMODULE-2'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE-2'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE-2'),
  ('sqlite3.__main__',
   'C:\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE-2'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE-2'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE-2'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE-2'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE-2'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE-2'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE-2'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE-2'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE-2'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE-2'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE-2'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE-2'),
  ('tkinter', 'C:\\Python313\\Lib\\tkinter\\__init__.py', 'PYMODULE-2'),
  ('tkinter.commondialog',
   'C:\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE-2'),
  ('tkinter.constants',
   'C:\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE-2'),
  ('tkinter.dialog', 'C:\\Python313\\Lib\\tkinter\\dialog.py', 'PYMODULE-2'),
  ('tkinter.filedialog',
   'C:\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE-2'),
  ('tkinter.messagebox',
   'C:\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE-2'),
  ('tkinter.simpledialog',
   'C:\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE-2'),
  ('tkinter.ttk', 'C:\\Python313\\Lib\\tkinter\\ttk.py', 'PYMODULE-2'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE-2'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE-2'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE-2'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE-2'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE-2'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE-2'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE-2'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE-2'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE-2'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE-2'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE-2'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE-2'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE-2'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE-2'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE-2'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE-2'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE-2'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE-2'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-2'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE-2'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE-2'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE-2'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE-2'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-2'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'C:\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE-2'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE-2'),
  ('xml.sax.saxutils',
   'C:\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE-2'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE-2'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE-2'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE-2')])
