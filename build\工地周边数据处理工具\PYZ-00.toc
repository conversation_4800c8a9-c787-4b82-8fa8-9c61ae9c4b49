('E:\\cursor_project\\方便截图软件\\build\\工地周边数据处理工具\\PYZ-00.pyz',
 [('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands', 'C:\\Python313\\Lib\\_pyrepl\\commands.py', 'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE'),
  ('_pyrepl.readline', 'C:\\Python313\\Lib\\_pyrepl\\readline.py', 'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python313\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'C:\\Python313\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'C:\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'C:\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'C:\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'C:\\Python313\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'C:\\Python313\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python313\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python313\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('geographiclib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\__init__.py',
   'PYMODULE'),
  ('geographiclib.accumulator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\accumulator.py',
   'PYMODULE'),
  ('geographiclib.constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\constants.py',
   'PYMODULE'),
  ('geographiclib.geodesic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesic.py',
   'PYMODULE'),
  ('geographiclib.geodesiccapability',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesiccapability.py',
   'PYMODULE'),
  ('geographiclib.geodesicline',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesicline.py',
   'PYMODULE'),
  ('geographiclib.geomath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geomath.py',
   'PYMODULE'),
  ('geographiclib.polygonarea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\polygonarea.py',
   'PYMODULE'),
  ('geopy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\__init__.py',
   'PYMODULE'),
  ('geopy.adapters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\adapters.py',
   'PYMODULE'),
  ('geopy.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\compat.py',
   'PYMODULE'),
  ('geopy.distance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\distance.py',
   'PYMODULE'),
  ('geopy.exc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\exc.py',
   'PYMODULE'),
  ('geopy.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\format.py',
   'PYMODULE'),
  ('geopy.geocoders',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\__init__.py',
   'PYMODULE'),
  ('geopy.geocoders.arcgis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\arcgis.py',
   'PYMODULE'),
  ('geopy.geocoders.azure',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\azure.py',
   'PYMODULE'),
  ('geopy.geocoders.baidu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\baidu.py',
   'PYMODULE'),
  ('geopy.geocoders.banfrance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\banfrance.py',
   'PYMODULE'),
  ('geopy.geocoders.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\base.py',
   'PYMODULE'),
  ('geopy.geocoders.bing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\bing.py',
   'PYMODULE'),
  ('geopy.geocoders.databc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\databc.py',
   'PYMODULE'),
  ('geopy.geocoders.geocodeearth',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geocodeearth.py',
   'PYMODULE'),
  ('geopy.geocoders.geocodio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geocodio.py',
   'PYMODULE'),
  ('geopy.geocoders.geokeo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geokeo.py',
   'PYMODULE'),
  ('geopy.geocoders.geolake',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geolake.py',
   'PYMODULE'),
  ('geopy.geocoders.geonames',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geonames.py',
   'PYMODULE'),
  ('geopy.geocoders.google',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\google.py',
   'PYMODULE'),
  ('geopy.geocoders.here',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\here.py',
   'PYMODULE'),
  ('geopy.geocoders.ignfrance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\ignfrance.py',
   'PYMODULE'),
  ('geopy.geocoders.mapbox',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\mapbox.py',
   'PYMODULE'),
  ('geopy.geocoders.mapquest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\mapquest.py',
   'PYMODULE'),
  ('geopy.geocoders.maptiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\maptiler.py',
   'PYMODULE'),
  ('geopy.geocoders.nominatim',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\nominatim.py',
   'PYMODULE'),
  ('geopy.geocoders.opencage',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\opencage.py',
   'PYMODULE'),
  ('geopy.geocoders.openmapquest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\openmapquest.py',
   'PYMODULE'),
  ('geopy.geocoders.pelias',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\pelias.py',
   'PYMODULE'),
  ('geopy.geocoders.photon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\photon.py',
   'PYMODULE'),
  ('geopy.geocoders.pickpoint',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\pickpoint.py',
   'PYMODULE'),
  ('geopy.geocoders.smartystreets',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\smartystreets.py',
   'PYMODULE'),
  ('geopy.geocoders.tomtom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\tomtom.py',
   'PYMODULE'),
  ('geopy.geocoders.what3words',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\what3words.py',
   'PYMODULE'),
  ('geopy.geocoders.woosmap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\woosmap.py',
   'PYMODULE'),
  ('geopy.geocoders.yandex',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\yandex.py',
   'PYMODULE'),
  ('geopy.location',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\location.py',
   'PYMODULE'),
  ('geopy.point',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\point.py',
   'PYMODULE'),
  ('geopy.timezone',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\timezone.py',
   'PYMODULE'),
  ('geopy.units',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\units.py',
   'PYMODULE'),
  ('geopy.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\util.py',
   'PYMODULE'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('pdb', 'C:\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'C:\\Python313\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'C:\\Python313\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE'),
  ('six',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'C:\\Python313\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'C:\\Python313\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog', 'C:\\Python313\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python313\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('unittest', 'C:\\Python313\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python313\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'C:\\Python313\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python313\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python313\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'C:\\Python313\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python313\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python313\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'C:\\Python313\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python313\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python313\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python313\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python313\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE')])
