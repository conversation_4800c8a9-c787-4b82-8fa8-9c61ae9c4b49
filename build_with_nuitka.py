#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 Nuitka 打包程序
Nuitka 可以将 Python 代码编译为真正的可执行文件，性能更好
"""

import subprocess
import sys
import os

def install_nuitka():
    """安装 Nuitka"""
    print("📦 正在安装 Nuitka...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'nuitka'], check=True)
        print("✅ Nuitka 安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ Nuitka 安装失败")
        return False

def build_with_nuitka():
    """使用 Nuitka 构建"""
    print("🔨 使用 Nuitka 开始编译...")
    
    cmd = [
        sys.executable, '-m', 'nuitka',
        '--standalone',  # 独立模式
        '--onefile',     # 单文件
        '--windows-disable-console',  # 不显示控制台
        '--output-filename=工地数据处理工具_Nuitka.exe',
    ]
    
    # 添加图标
    if os.path.exists('icon.ico'):
        cmd.extend(['--windows-icon-from-ico=icon.ico'])
    
    # 包含数据文件
    if os.path.exists('icon.ico'):
        cmd.extend(['--include-data-file=icon.ico=icon.ico'])
    
    # 主文件
    cmd.append('main.py')
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ Nuitka 编译成功！")
        print("📁 可执行文件: 工地数据处理工具_Nuitka.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Nuitka 编译失败: {e}")
        return False

def main():
    print("🚀 Nuitka 编译工具")
    print("=" * 40)
    print("Nuitka 优势：")
    print("• 真正的编译，不是打包")
    print("• 启动速度更快")
    print("• 运行性能更好")
    print("• 文件大小通常更小")
    print()
    
    # 检查是否安装了 Nuitka
    try:
        import nuitka
        print("✅ Nuitka 已安装")
    except ImportError:
        if not install_nuitka():
            return
    
    # 开始构建
    if build_with_nuitka():
        print("\n🎉 编译完成！")
        print("💡 提示：Nuitka 编译的程序通常比 PyInstaller 打包的程序运行更快")
    else:
        print("\n❌ 编译失败")
        print("💡 提示：如果 Nuitka 编译失败，可以尝试其他打包方式")

if __name__ == '__main__':
    main()
