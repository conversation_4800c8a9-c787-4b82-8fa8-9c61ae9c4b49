#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键打包工具 - 最简单的方式
只需运行这个脚本，自动完成所有打包工作
"""

import subprocess
import sys
import os
import time

def run_command(cmd, description):
    """运行命令并显示进度"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} 出错: {e}")
        return False

def main():
    print("🚀 一键打包工具")
    print("=" * 40)
    
    # 检查主文件
    if not os.path.exists("main.py"):
        print("❌ 找不到 main.py 文件")
        return
    
    print("📋 开始自动打包流程...")
    
    # 1. 安装 PyInstaller
    if not run_command(f"{sys.executable} -m pip install pyinstaller", "安装 PyInstaller"):
        return
    
    # 2. 清理旧文件
    if os.path.exists("dist"):
        run_command("rmdir /s /q dist", "清理旧的 dist 目录")
    if os.path.exists("build"):
        run_command("rmdir /s /q build", "清理旧的 build 目录")
    
    # 3. 构建基本命令
    cmd_parts = [
        f"{sys.executable} -m PyInstaller",
        "--onefile",  # 单文件模式
        "--windowed",  # 不显示控制台
        "--clean",  # 清理缓存
    ]
    
    # 4. 添加图标（如果存在）
    if os.path.exists("icon.ico"):
        cmd_parts.append("--icon=icon.ico")
        cmd_parts.append("--add-data=icon.ico;.")
        print("✅ 找到图标文件，将包含在程序中")
    
    # 5. 设置程序名称
    cmd_parts.append("--name=工地数据处理工具")
    
    # 6. 主文件
    cmd_parts.append("main.py")
    
    # 7. 执行打包
    build_cmd = " ".join(cmd_parts)
    print(f"🔨 执行打包命令...")
    print(f"命令: {build_cmd}")
    
    start_time = time.time()
    
    if run_command(build_cmd, "程序打包"):
        end_time = time.time()
        build_time = end_time - start_time
        
        print("\n🎉 打包成功！")
        print(f"⏱️  用时: {build_time:.1f} 秒")
        
        # 检查生成的文件
        exe_path = "dist/工地数据处理工具.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"📁 文件位置: {exe_path}")
            print(f"📊 文件大小: {file_size:.2f} MB")
            
            # 询问是否运行
            try:
                choice = input("\n🚀 是否立即运行程序？(y/n): ").lower()
                if choice in ['y', 'yes', '是']:
                    print("🏃 启动程序...")
                    subprocess.Popen([exe_path])
            except KeyboardInterrupt:
                print("\n👋 再见！")
        else:
            print("❌ 未找到生成的可执行文件")
    else:
        print("❌ 打包失败，请检查错误信息")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
    
    input("\n按回车键退出...")
