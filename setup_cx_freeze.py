#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 cx_Freeze 打包程序
这是另一种简单的打包方式，特别适合跨平台
"""

import sys
from cx_Freeze import setup, Executable
import os

# 程序信息
APP_NAME = "工地周边数据处理工具"
VERSION = "2.0.0"
DESCRIPTION = "智能匹配走航数据与工地台账信息"

# 包含的文件
include_files = []
if os.path.exists("icon.ico"):
    include_files.append("icon.ico")

# 构建选项
build_exe_options = {
    "packages": [
        "pandas", 
        "numpy", 
        "geopy", 
        "openpyxl", 
        "tkinter",
        "multiprocessing"
    ],
    "excludes": [
        "matplotlib", 
        "scipy", 
        "pytest", 
        "jupyter",
        "IPython"
    ],
    "include_files": include_files,
    "optimize": 2,
}

# 可执行文件配置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 不显示控制台窗口

exe = Executable(
    script="main.py",
    base=base,
    target_name=f"{APP_NAME}.exe",
    icon="icon.ico" if os.path.exists("icon.ico") else None
)

# 设置配置
setup(
    name=APP_NAME,
    version=VERSION,
    description=DESCRIPTION,
    options={"build_exe": build_exe_options},
    executables=[exe]
)

print(f"""
🎉 cx_Freeze 配置完成！

使用方法：
1. 安装 cx_Freeze: pip install cx_Freeze
2. 运行打包命令: python setup_cx_freeze.py build

打包后的文件将在 build 目录中。
""")
