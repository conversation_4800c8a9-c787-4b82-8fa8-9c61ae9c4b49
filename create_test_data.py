#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试数据文件
用于验证程序的数据处理功能
"""

import pandas as pd
import numpy as np
import os

def create_test_track_data():
    """创建测试走航数据"""
    print("📊 创建测试走航数据...")
    
    # 模拟北京地区的走航轨迹
    base_lat = 39.9042  # 天安门纬度
    base_lon = 116.4074  # 天安门经度
    
    # 生成100个轨迹点
    np.random.seed(42)
    track_data = []
    
    for i in range(100):
        # 在基准点周围随机生成轨迹点
        lat = base_lat + np.random.normal(0, 0.01)  # 约1km范围内
        lon = base_lon + np.random.normal(0, 0.01)
        
        track_data.append({
            '纬度': lat,
            '经度': lon,
            '时间': f'2025-01-{(i % 30) + 1:02d} {(i % 24):02d}:00:00',
            '速度': np.random.randint(0, 60),
            '其他数据': f'轨迹点{i+1}'
        })
    
    df = pd.DataFrame(track_data)
    df.to_csv('测试走航数据.csv', index=False, encoding='gbk')
    print(f"✅ 测试走航数据已创建: 测试走航数据.csv ({len(df)} 条记录)")
    return df

def create_test_sites_data():
    """创建测试工地台账数据"""
    print("🏗️ 创建测试工地台账数据...")
    
    # 模拟北京地区的工地
    base_lat = 39.9042
    base_lon = 116.4074
    
    sites_data = []
    
    # 创建一些在轨迹范围内的工地
    for i in range(10):
        lat = base_lat + np.random.normal(0, 0.008)  # 稍小的范围确保有匹配
        lon = base_lon + np.random.normal(0, 0.008)
        
        sites_data.append({
            '工地名称': f'测试工地{i+1}',
            '纬度': lat,
            '经度': lon,
            '工地类型': ['住宅', '商业', '基础设施'][i % 3],
            '施工状态': ['在建', '停工', '完工'][i % 3],
            '负责人': f'负责人{i+1}',
            '联系电话': f'138{i:04d}{i:04d}'
        })
    
    # 添加一些有问题的数据来测试数据清洗功能
    sites_data.append({
        '工地名称': '',  # 空名称
        '纬度': base_lat,
        '经度': base_lon,
        '工地类型': '测试',
        '施工状态': '测试',
        '负责人': '测试',
        '联系电话': '测试'
    })
    
    sites_data.append({
        '工地名称': '边界测试工地',
        '纬度': 200,  # 无效纬度
        '经度': base_lon,
        '工地类型': '测试',
        '施工状态': '测试',
        '负责人': '测试',
        '联系电话': '测试'
    })
    
    sites_data.append({
        '工地名称': '正常工地',
        '纬度': base_lat + 0.001,
        '经度': base_lon + 0.001,
        '工地类型': '正常',
        '施工状态': '在建',
        '负责人': '张三',
        '联系电话': '13800138000'
    })
    
    df = pd.DataFrame(sites_data)
    df.to_excel('测试工地台账.xlsx', index=False)
    print(f"✅ 测试工地台账已创建: 测试工地台账.xlsx ({len(df)} 条记录)")
    return df

def create_problematic_data():
    """创建有问题的数据文件用于测试错误处理"""
    print("⚠️ 创建问题数据文件用于测试...")
    
    # 创建缺少列的文件
    bad_data = pd.DataFrame({
        '名称': ['工地1', '工地2'],  # 错误的列名
        '纬度': [39.9, 39.91],
        '经度': [116.4, 116.41]
    })
    bad_data.to_excel('错误格式工地台账.xlsx', index=False)
    
    # 创建包含非字符串工地名称的文件
    mixed_data = pd.DataFrame({
        '工地名称': [123, '正常工地', None, 456.789],  # 混合数据类型
        '纬度': [39.9, 39.91, 39.92, 39.93],
        '经度': [116.4, 116.41, 116.42, 116.43]
    })
    mixed_data.to_excel('混合类型工地台账.xlsx', index=False)
    
    print("✅ 问题数据文件已创建用于测试错误处理")

def main():
    print("🚀 创建测试数据文件")
    print("=" * 40)
    
    try:
        # 创建正常的测试数据
        track_df = create_test_track_data()
        sites_df = create_test_sites_data()
        
        # 创建问题数据用于测试
        create_problematic_data()
        
        print("\n📋 测试数据文件创建完成：")
        print("✅ 测试走航数据.csv - 正常的走航轨迹数据")
        print("✅ 测试工地台账.xlsx - 正常的工地台账数据")
        print("⚠️ 错误格式工地台账.xlsx - 缺少必要列的错误数据")
        print("⚠️ 混合类型工地台账.xlsx - 包含非字符串工地名称的数据")
        
        print("\n🎯 使用建议：")
        print("1. 先用正常数据测试程序基本功能")
        print("2. 再用问题数据测试错误处理功能")
        print("3. 建议搜索半径设置为 500-1000 米")
        
        print("\n📊 数据统计：")
        print(f"• 走航轨迹点数：{len(track_df)}")
        print(f"• 工地数量：{len(sites_df)}")
        print(f"• 预期匹配结果：应该能找到一些匹配的轨迹点")
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")

if __name__ == '__main__':
    main()
