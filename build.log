2025-07-29 11:35:14,809 - INFO - ============================================================
2025-07-29 11:35:14,810 - INFO - 开始构建工地周边数据处理工具
2025-07-29 11:35:14,810 - INFO - ============================================================
2025-07-29 11:35:14,810 - INFO - 检查构建环境...
2025-07-29 11:35:14,810 - INFO - Python版本: 3.13.5
2025-07-29 11:35:14,810 - INFO - 图标文件检查通过
2025-07-29 11:35:14,810 - INFO - 环境检查通过
2025-07-29 11:35:14,811 - INFO - 检查和安装依赖包...
2025-07-29 11:35:15,481 - INFO - 安装项目依赖...
2025-07-29 11:35:23,888 - INFO - 依赖安装完成
2025-07-29 11:35:23,888 - INFO - 清理构建目录...
2025-07-29 11:35:23,894 - INFO - 已清理: E:\cursor_project\方便截图软件\dist
2025-07-29 11:35:23,904 - INFO - 已清理: E:\cursor_project\方便截图软件\build
2025-07-29 11:35:23,904 - INFO - 开始构建应用程序...
2025-07-29 11:35:23,904 - INFO - 执行命令: E:\cursor_project\方便截图软件\venv\Scripts\python.exe -m PyInstaller --clean --noconfirm E:\cursor_project\方便截图软件\工地周边数据处理工具.spec
2025-07-29 11:35:24,725 - ERROR - 构建失败！
2025-07-29 11:35:24,726 - ERROR - 错误输出: 241 INFO: PyInstaller: 6.14.2, contrib hooks: 2025.5
241 INFO: Python: 3.13.5
300 INFO: Platform: Windows-11-10.0.22631-SP0
300 INFO: Python environment: E:\cursor_project\方便截图软件\venv
304 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "E:\cursor_project\方便截图软件\venv\Lib\site-packages\PyInstaller\__main__.py", line 321, in <module>
    run()
    ~~~^^
  File "E:\cursor_project\方便截图软件\venv\Lib\site-packages\PyInstaller\__main__.py", line 215, in run
    run_build(pyi_config, spec_file, **vars(args))
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\cursor_project\方便截图软件\venv\Lib\site-packages\PyInstaller\__main__.py", line 70, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\cursor_project\方便截图软件\venv\Lib\site-packages\PyInstaller\building\build_main.py", line 1282, in main
    build(specfile, distpath, workpath, clean_build)
    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\cursor_project\方便截图软件\venv\Lib\site-packages\PyInstaller\building\build_main.py", line 1220, in build
    exec(code, spec_namespace)
    ~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "E:\cursor_project\方便截图软件\工地周边数据处理工具.spec", line 15, in <module>
    project_root = Path(__file__).parent
                        ^^^^^^^^
NameError: name '__file__' is not defined. Did you mean: '__name__'?

2025-07-29 11:35:50,626 - INFO - ============================================================
2025-07-29 11:35:50,626 - INFO - 开始构建工地周边数据处理工具
2025-07-29 11:35:50,627 - INFO - ============================================================
2025-07-29 11:35:50,627 - INFO - 检查构建环境...
2025-07-29 11:35:50,627 - INFO - Python版本: 3.13.5
2025-07-29 11:35:50,627 - INFO - 图标文件检查通过
2025-07-29 11:35:50,627 - INFO - 环境检查通过
2025-07-29 11:35:50,627 - INFO - 检查和安装依赖包...
2025-07-29 11:35:51,179 - INFO - 安装项目依赖...
2025-07-29 11:35:54,460 - INFO - 依赖安装完成
2025-07-29 11:35:54,460 - INFO - 清理构建目录...
2025-07-29 11:35:54,461 - INFO - 已清理: E:\cursor_project\方便截图软件\dist
2025-07-29 11:35:54,461 - INFO - 已清理: E:\cursor_project\方便截图软件\build
2025-07-29 11:35:54,461 - INFO - 开始构建应用程序...
2025-07-29 11:35:54,462 - INFO - 执行命令: E:\cursor_project\方便截图软件\venv\Scripts\python.exe -m PyInstaller --clean --noconfirm E:\cursor_project\方便截图软件\工地周边数据处理工具.spec
2025-07-29 11:36:40,558 - INFO - 构建成功！用时: 46.1秒
2025-07-29 11:36:40,558 - INFO - 验证构建结果...
2025-07-29 11:36:40,559 - INFO - 找到可执行文件: E:\cursor_project\方便截图软件\dist\工地周边数据处理工具_v2.0.exe
2025-07-29 11:36:40,559 - INFO - 文件大小: 33.36 MB
2025-07-29 11:36:40,559 - INFO - 测试程序启动...
2025-07-29 11:36:46,479 - INFO - 构建验证完成
2025-07-29 11:36:46,479 - INFO - 生成构建信息...
2025-07-29 11:36:46,480 - INFO - 构建信息已保存: E:\cursor_project\方便截图软件\dist\BUILD_INFO.md
2025-07-29 11:36:46,480 - INFO - ============================================================
2025-07-29 11:36:46,480 - INFO - 构建完成！
2025-07-29 11:36:46,481 - INFO - 可执行文件位置: E:\cursor_project\方便截图软件\dist
2025-07-29 11:36:46,481 - INFO - ============================================================
2025-07-29 12:00:13,832 - INFO - ============================================================
2025-07-29 12:00:13,833 - INFO - 开始构建工地周边数据处理工具
2025-07-29 12:00:13,833 - INFO - ============================================================
2025-07-29 12:00:13,833 - INFO - 检查构建环境...
2025-07-29 12:00:13,833 - INFO - Python版本: 3.13.5
2025-07-29 12:00:13,834 - INFO - 图标文件检查通过
2025-07-29 12:00:13,834 - INFO - 环境检查通过
2025-07-29 12:00:13,834 - INFO - 检查和安装依赖包...
2025-07-29 12:00:14,370 - INFO - 安装项目依赖...
2025-07-29 12:00:17,552 - INFO - 依赖安装完成
2025-07-29 12:00:17,552 - INFO - 清理构建目录...
2025-07-29 12:00:17,556 - INFO - 已清理: E:\cursor_project\方便截图软件\dist
2025-07-29 12:00:17,562 - INFO - 已清理: E:\cursor_project\方便截图软件\build
2025-07-29 12:00:17,562 - INFO - 开始构建应用程序...
2025-07-29 12:00:17,563 - INFO - 执行命令: E:\cursor_project\方便截图软件\venv\Scripts\python.exe -m PyInstaller --clean --noconfirm E:\cursor_project\方便截图软件\工地周边数据处理工具.spec
2025-07-29 12:00:57,041 - INFO - 构建成功！用时: 39.5秒
2025-07-29 12:00:57,042 - INFO - 验证构建结果...
2025-07-29 12:00:57,042 - INFO - 找到可执行文件: E:\cursor_project\方便截图软件\dist\工地周边数据处理工具_v2.0.exe
2025-07-29 12:00:57,042 - INFO - 文件大小: 33.35 MB
2025-07-29 12:00:57,043 - INFO - 测试程序启动...
2025-07-29 12:02:03,550 - INFO - 程序启动测试完成（超时正常，GUI程序）
2025-07-29 12:02:03,550 - INFO - 构建验证完成
2025-07-29 12:02:03,550 - INFO - 生成构建信息...
2025-07-29 12:02:03,552 - INFO - 构建信息已保存: E:\cursor_project\方便截图软件\dist\BUILD_INFO.md
2025-07-29 12:02:03,553 - INFO - ============================================================
2025-07-29 12:02:03,553 - INFO - 构建完成！
2025-07-29 12:02:03,553 - INFO - 可执行文件位置: E:\cursor_project\方便截图软件\dist
2025-07-29 12:02:03,553 - INFO - ============================================================
2025-07-29 12:03:22,561 - INFO - ============================================================
2025-07-29 12:03:22,562 - INFO - 开始构建工地周边数据处理工具
2025-07-29 12:03:22,562 - INFO - ============================================================
2025-07-29 12:03:22,562 - INFO - 检查构建环境...
2025-07-29 12:03:22,562 - INFO - Python版本: 3.13.5
2025-07-29 12:03:22,562 - INFO - 图标文件检查通过
2025-07-29 12:03:22,563 - INFO - 环境检查通过
2025-07-29 12:03:22,563 - INFO - 检查和安装依赖包...
2025-07-29 12:03:23,060 - INFO - 安装项目依赖...
2025-07-29 12:03:26,335 - INFO - 依赖安装完成
2025-07-29 12:03:26,335 - INFO - 清理构建目录...
2025-07-29 12:03:26,339 - INFO - 已清理: E:\cursor_project\方便截图软件\dist
2025-07-29 12:03:26,346 - INFO - 已清理: E:\cursor_project\方便截图软件\build
2025-07-29 12:03:26,346 - INFO - 开始构建应用程序...
2025-07-29 12:03:26,346 - INFO - 执行命令: E:\cursor_project\方便截图软件\venv\Scripts\python.exe -m PyInstaller --clean --noconfirm E:\cursor_project\方便截图软件\工地周边数据处理工具.spec
2025-07-29 12:04:07,227 - INFO - 构建成功！用时: 40.9秒
2025-07-29 12:04:07,228 - INFO - 验证构建结果...
2025-07-29 12:04:07,228 - INFO - 找到可执行文件: E:\cursor_project\方便截图软件\dist\工地周边数据处理工具_v2.0.exe
2025-07-29 12:04:07,228 - INFO - 文件大小: 33.48 MB
2025-07-29 12:04:07,228 - INFO - 测试程序启动...
2025-07-29 12:04:16,352 - INFO - 构建验证完成
2025-07-29 12:04:16,352 - INFO - 生成构建信息...
2025-07-29 12:04:16,353 - INFO - 构建信息已保存: E:\cursor_project\方便截图软件\dist\BUILD_INFO.md
2025-07-29 12:04:16,353 - INFO - ============================================================
2025-07-29 12:04:16,353 - INFO - 构建完成！
2025-07-29 12:04:16,353 - INFO - 可执行文件位置: E:\cursor_project\方便截图软件\dist
2025-07-29 12:04:16,353 - INFO - ============================================================
2025-07-29 12:18:38,928 - INFO - ============================================================
2025-07-29 12:18:38,928 - INFO - 开始构建工地周边数据处理工具
2025-07-29 12:18:38,928 - INFO - ============================================================
2025-07-29 12:18:38,928 - INFO - 检查构建环境...
2025-07-29 12:18:38,929 - INFO - Python版本: 3.13.5
2025-07-29 12:18:38,929 - INFO - 图标文件检查通过
2025-07-29 12:18:38,930 - INFO - 环境检查通过
2025-07-29 12:18:38,930 - INFO - 检查和安装依赖包...
2025-07-29 12:18:41,161 - INFO - 安装项目依赖...
2025-07-29 12:18:45,992 - INFO - 依赖安装完成
2025-07-29 12:18:45,993 - INFO - 清理构建目录...
2025-07-29 12:18:45,998 - INFO - 已清理: E:\cursor_project\方便截图软件\dist
2025-07-29 12:18:46,004 - INFO - 已清理: E:\cursor_project\方便截图软件\build
2025-07-29 12:18:46,005 - INFO - 开始构建应用程序...
2025-07-29 12:18:46,005 - INFO - 执行命令: E:\cursor_project\方便截图软件\venv\Scripts\python.exe -m PyInstaller --clean --noconfirm E:\cursor_project\方便截图软件\工地周边数据处理工具.spec
2025-07-29 12:19:49,069 - INFO - 构建成功！用时: 63.1秒
2025-07-29 12:19:49,069 - INFO - 验证构建结果...
2025-07-29 12:19:49,069 - INFO - 找到可执行文件: E:\cursor_project\方便截图软件\dist\工地周边数据处理工具_v2.0.exe
2025-07-29 12:19:49,070 - INFO - 文件大小: 33.48 MB
2025-07-29 12:19:49,070 - INFO - 测试程序启动...
2025-07-29 12:19:57,055 - INFO - 构建验证完成
2025-07-29 12:19:57,056 - INFO - 生成构建信息...
2025-07-29 12:19:57,057 - INFO - 构建信息已保存: E:\cursor_project\方便截图软件\dist\BUILD_INFO.md
2025-07-29 12:19:57,057 - INFO - ============================================================
2025-07-29 12:19:57,057 - INFO - 构建完成！
2025-07-29 12:19:57,057 - INFO - 可执行文件位置: E:\cursor_project\方便截图软件\dist
2025-07-29 12:19:57,057 - INFO - ============================================================
