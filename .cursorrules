一、项目目标
开发一个自动化工具，实现以下功能：

批量读取工地台账中的经纬度信息
自动筛选走航数据中每个工地周边500米内的所有负荷数据
生成Excel数据报表 + 交互式地图截图
支持同时处理成百上千个工地信息
二、开发环境配置
安装Python

创建 requirements.txt 文件：

TEXT
pandas==1.5.3       # 数据处理
geopy==2.3.0        # 地理距离计算
folium==0.14.0      # 地图可视化
openpyxl==3.1.2     # Excel文件处理
selenium==4.9.0     # 浏览器自动化截图
webdriver-manager==3.8.6  # 自动管理浏览器驱动
终端执行安装命令：

BASH
pip install -r requirements.txt
三、项目文件结构
BASH
工地数据分析工具/
├── data/                # 原始数据存放
│   ├── 走航数据.csv    # 字段：时间,经度,纬度,负荷
│   └── 工地台账.csv    # 字段：工地名称,经度,纬度
│
├── outputs/             # 自动化生成文件
│   ├── 工地A_数据.xlsx
│   ├── 工地A_地图.html
│   └── 工地A_截图.png
│
└── main.py              # 主程序代码
四、核心代码逻辑
数据加载模块

PYTHON
# 读取走航数据（每秒一条）
df_track = pd.read_csv("data/走航数据.csv")

# 读取工地台账
df_sites = pd.read_csv("data/工地台账.csv")
地理距离计算

PYTHON
from geopy.distance import geodesic

# 计算两点间地面距离（单位：米）
def calc_distance(row, site_lat, site_lon):
    return geodesic(
        (row['纬度'], row['经度']), 
        (site_lat, site_lon)
    ).meters
地图可视化模块

PYTHON
def generate_map(site_info, points):
    # 创建地图画布
    m = folium.Map(
        location=[site_info['纬度'], site_info['经度']],
        zoom_start=16,
        tiles='Stamen Terrain'
    )
    
    # 标注工地位置
    folium.Marker(
        [site_info['纬度'], site_info['经度']],
        popup=site_info['工地名称'], 
        icon=folium.Icon(color='blue')
    ).add_to(m)
    
    # 绘制走航点轨迹
    for point in points:
        folium.CircleMarker(
            location=[point['纬度'], point['经度']],
            radius=2,
            color='#FF0000',
            fill=True
        ).add_to(m)
    
    return m
自动截图功能

PYTHON
from selenium import webdriver

def save_map_screenshot(html_path, output_path):
    # 使用Chrome浏览器渲染地图
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')  # 无界面模式
    
    driver = webdriver.Chrome(options=options)
    driver.get(f"file://{html_path}")
    driver.set_window_size(1200, 800)  # 控制截图分辨率
    driver.save_screenshot(output_path)
    driver.quit()
五、操作流程
准备数据

将原始数据放入 data/ 目录
确认CSV文件字段名称正确
运行程序

BASH
python main.py
查看结果

Excel报表：outputs/工地名称_数据.xlsx
交互地图：outputs/工地名称_地图.html（双击可用浏览器打开）
截图文件：outputs/工地名称_截图.png
六、自定义设置
通过修改代码中的参数实现定制化：

PYTHON
# 在main.py开头处修改这些参数
CONFIG = {
    "search_radius": 500,     # 单位：米
    "map_zoom_level": 16,     # 地图默认缩放级别(12-18)
    "screenshot_size": (1200, 800),  # 截图分辨率(宽,高)
    "heatmap_enable": True    # 是否生成热力图
}
七、Cursor IDE 特别优化
智能补全配置

创建 .cursor/autocomplete.json：
JSON
{
  "python": {
    "snippets": {
      "geo-filter": {
        "prefix": "geofilter",
        "body": "df['distance'] = df.apply(lambda row: geodesic((row['lat'], row['lon']), ($1, $2)).meters, axis=1)"
      }
    }
  }
}
AI辅助指令示例

优化代码性能：
TEXT
/fix 如何加速geopy的距离计算？
修改地图样式：
TEXT
/ask 把地图底图改成高德地图样式
处理异常数据：
TEXT
/fix 如果遇到经纬度为null的情况怎么跳过？
八、常见问题解决
浏览器驱动报错

确保已安装Chrome浏览器
运行 webdriver-manager update 更新驱动
数据加载失败

检查CSV文件是否为UTF-8编码
确认经纬度字段名与代码一致
地图截图空白

增加等待时间确保地图加载完成：
PYTHON
driver.get(url)
time.sleep(2)  # 等待2秒
driver.save_screenshot()
九、升级扩展建议
性能优化方向

采用GeoPandas进行空间索引加速
使用多线程处理多个工地
功能增强

添加噪声过滤（排除异常波动数据）
生成时间趋势分析图表
导出PDF格式的综合报告