#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工地周边数据处理工具 - 自动化打包脚本
版本: 2.0.0
作者: AI Assistant
日期: 2025-01-29

此脚本用于自动化构建和打包应用程序
包含环境检查、依赖安装、打包执行和测试验证等功能
"""

import os
import sys
import subprocess
import shutil
import time
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class AppBuilder:
    """应用程序构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / 'dist'
        self.build_dir = self.project_root / 'build'
        self.spec_file = self.project_root / '工地周边数据处理工具.spec'
        self.main_file = self.project_root / 'main.py'
        self.icon_file = self.project_root / 'icon.ico'
        
    def check_environment(self) -> bool:
        """检查构建环境"""
        logger.info("检查构建环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            logger.error(f"Python版本过低: {python_version.major}.{python_version.minor}")
            logger.error("需要Python 3.8或更高版本")
            return False
        
        logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查必要文件
        required_files = [self.main_file, self.spec_file]
        for file_path in required_files:
            if not file_path.exists():
                logger.error(f"缺少必要文件: {file_path}")
                return False
        
        # 检查图标文件
        if not self.icon_file.exists():
            logger.warning(f"图标文件不存在: {self.icon_file}")
        else:
            logger.info("图标文件检查通过")
        
        logger.info("环境检查通过")
        return True
    
    def install_dependencies(self) -> bool:
        """安装依赖包"""
        logger.info("检查和安装依赖包...")
        
        try:
            # 检查PyInstaller
            result = subprocess.run([sys.executable, '-c', 'import PyInstaller'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                logger.info("安装PyInstaller...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], 
                             check=True)
            
            # 安装其他依赖
            requirements_file = self.project_root / 'requirements.txt'
            if requirements_file.exists():
                logger.info("安装项目依赖...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', str(requirements_file)], 
                             check=True)
            
            logger.info("依赖安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"依赖安装失败: {e}")
            return False
    
    def clean_build_dirs(self) -> None:
        """清理构建目录"""
        logger.info("清理构建目录...")
        
        for dir_path in [self.dist_dir, self.build_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                logger.info(f"已清理: {dir_path}")
    
    def build_application(self) -> bool:
        """构建应用程序"""
        logger.info("开始构建应用程序...")
        
        try:
            # 执行PyInstaller
            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--clean',  # 清理缓存
                '--noconfirm',  # 不询问覆盖
                str(self.spec_file)
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(cmd, cwd=self.project_root, 
                                  capture_output=True, text=True)
            
            build_time = time.time() - start_time
            
            if result.returncode == 0:
                logger.info(f"构建成功！用时: {build_time:.1f}秒")
                
                # 输出构建信息
                if result.stdout:
                    logger.info("构建输出:")
                    for line in result.stdout.split('\n')[-10:]:  # 显示最后10行
                        if line.strip():
                            logger.info(f"  {line}")
                
                return True
            else:
                logger.error("构建失败！")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"构建过程出错: {e}")
            return False
    
    def verify_build(self) -> bool:
        """验证构建结果"""
        logger.info("验证构建结果...")
        
        # 查找可执行文件
        exe_files = list(self.dist_dir.glob('*.exe'))
        if not exe_files:
            logger.error("未找到可执行文件")
            return False
        
        exe_file = exe_files[0]
        logger.info(f"找到可执行文件: {exe_file}")
        
        # 检查文件大小
        file_size = exe_file.stat().st_size
        size_mb = file_size / (1024 * 1024)
        logger.info(f"文件大小: {size_mb:.2f} MB")
        
        if size_mb > 200:  # 如果超过200MB，给出警告
            logger.warning("可执行文件较大，可能包含不必要的依赖")
        
        # 尝试运行程序（快速测试）
        try:
            logger.info("测试程序启动...")
            result = subprocess.run([str(exe_file), '--help'], 
                                  capture_output=True, text=True, timeout=10)
            # 注意：我们的GUI程序可能不支持--help参数，这里只是测试能否启动
        except subprocess.TimeoutExpired:
            logger.info("程序启动测试完成（超时正常，GUI程序）")
        except Exception as e:
            logger.warning(f"程序测试警告: {e}")
        
        logger.info("构建验证完成")
        return True
    
    def generate_build_info(self) -> None:
        """生成构建信息文件"""
        logger.info("生成构建信息...")
        
        build_info = f"""# 构建信息

## 应用程序信息
- 名称: 工地周边数据处理工具
- 版本: 2.0.0
- 构建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
- Python版本: {sys.version}

## 构建环境
- 操作系统: {os.name}
- 平台: {sys.platform}
- 架构: {os.environ.get('PROCESSOR_ARCHITECTURE', 'unknown')}

## 文件信息
"""
        
        # 添加文件信息
        exe_files = list(self.dist_dir.glob('*.exe'))
        if exe_files:
            exe_file = exe_files[0]
            file_size = exe_file.stat().st_size
            build_info += f"- 可执行文件: {exe_file.name}\n"
            build_info += f"- 文件大小: {file_size / (1024 * 1024):.2f} MB\n"
        
        # 保存构建信息
        info_file = self.dist_dir / 'BUILD_INFO.md'
        info_file.write_text(build_info, encoding='utf-8')
        logger.info(f"构建信息已保存: {info_file}")
    
    def build(self) -> bool:
        """执行完整的构建流程"""
        logger.info("=" * 60)
        logger.info("开始构建工地周边数据处理工具")
        logger.info("=" * 60)
        
        try:
            # 1. 环境检查
            if not self.check_environment():
                return False
            
            # 2. 安装依赖
            if not self.install_dependencies():
                return False
            
            # 3. 清理构建目录
            self.clean_build_dirs()
            
            # 4. 构建应用程序
            if not self.build_application():
                return False
            
            # 5. 验证构建结果
            if not self.verify_build():
                return False
            
            # 6. 生成构建信息
            self.generate_build_info()
            
            logger.info("=" * 60)
            logger.info("构建完成！")
            logger.info(f"可执行文件位置: {self.dist_dir}")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"构建失败: {e}")
            return False


def main():
    """主函数"""
    builder = AppBuilder()
    success = builder.build()
    
    if success:
        print("\n🎉 构建成功！")
        print(f"📁 可执行文件位置: {builder.dist_dir}")
        print("✅ 可以开始使用您的应用程序了！")
        sys.exit(0)
    else:
        print("\n❌ 构建失败！")
        print("📋 请查看 build.log 文件获取详细错误信息")
        sys.exit(1)


if __name__ == '__main__':
    main()
