"""
工地周边数据处理工具
用于匹配走航数据和工地台账，找出指定半径范围内的工地信息

Author: AI Assistant
Version: 2.0.0
Date: 2025-01-29
"""

import pandas as pd
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from geopy.distance import geodesic
import os
from multiprocessing import Pool, cpu_count
import sys
from typing import Optional, Tuple, List, Callable, Dict, Any
import logging
from pathlib import Path
import threading
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class Config:
    """应用程序配置类"""

    # 数据处理配置
    DEFAULT_RADIUS: int = 500
    MIN_RADIUS: int = 100
    MAX_RADIUS: int = 2000
    RADIUS_STEP: int = 50

    # 界面配置
    WINDOW_SIZE: str = "900x700"
    MIN_WINDOW_SIZE: str = "800x600"
    PADDING: int = 20

    # 样式配置
    STYLE: Dict[str, Any] = {
        'bg_color': '#f8f9fa',
        'primary_color': '#007bff',
        'success_color': '#28a745',
        'warning_color': '#ffc107',
        'danger_color': '#dc3545',
        'font_family': '微软雅黑',
        'normal_font_size': 10,
        'header_font_size': 14,
        'button_font_size': 11
    }

    # 文件配置
    SUPPORTED_CSV_EXTENSIONS: Tuple[str, ...] = ('.csv',)
    SUPPORTED_EXCEL_EXTENSIONS: Tuple[str, ...] = ('.xlsx', '.xls')

    # 性能配置
    MAX_WORKERS: int = min(cpu_count(), 8)  # 限制最大进程数
    CHUNK_SIZE: int = 1000  # 数据块大小

def calculate_distances(args: Tuple[Tuple[float, float], Tuple[float, float]]) -> float:
    """
    计算两个地理坐标点之间的距离

    Args:
        args: 包含两个坐标点的元组 ((lat1, lon1), (lat2, lon2))

    Returns:
        float: 距离（米）
    """
    track_coords, site_coords = args
    return geodesic(track_coords, site_coords).meters


class DataValidator:
    """数据验证类 - 增强版本"""

    @staticmethod
    def validate_csv_file(file_path: str) -> Tuple[bool, str]:
        """
        验证CSV文件格式

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            if not Path(file_path).exists():
                return False, "文件不存在"

            # 检查文件大小
            file_size = Path(file_path).stat().st_size
            if file_size == 0:
                return False, "文件为空"

            if file_size > 100 * 1024 * 1024:  # 100MB
                logger.warning(f"文件较大 ({file_size / 1024 / 1024:.1f}MB)，处理可能需要较长时间")

            # 尝试多种编码格式
            encodings = ['gbk', 'utf-8', 'gb2312', 'utf-8-sig']
            df = None
            used_encoding = None

            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding, nrows=5)
                    used_encoding = encoding
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                    continue

            if df is None:
                return False, "无法读取文件，请检查文件编码格式"

            logger.info(f"使用编码 {used_encoding} 成功读取CSV文件")

            # 验证必需列
            required_columns = ['纬度', '经度']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                return False, f"缺少必需的列: {', '.join(missing_columns)}"

            # 验证数据类型和范围
            lat_col, lon_col = '纬度', '经度'

            # 检查坐标数据是否为数值类型
            try:
                lat_values = pd.to_numeric(df[lat_col], errors='coerce')
                lon_values = pd.to_numeric(df[lon_col], errors='coerce')

                if lat_values.isna().any() or lon_values.isna().any():
                    return False, "坐标数据包含非数值内容"

                # 检查坐标范围（中国境内大致范围）
                if not (-90 <= lat_values.min() <= lat_values.max() <= 90):
                    return False, "纬度数据超出有效范围 (-90 到 90)"

                if not (-180 <= lon_values.min() <= lon_values.max() <= 180):
                    return False, "经度数据超出有效范围 (-180 到 180)"

                # 中国境内坐标范围检查
                if not (3 <= lat_values.min() and lat_values.max() <= 54):
                    logger.warning("纬度数据可能不在中国境内范围")

                if not (73 <= lon_values.min() and lon_values.max() <= 136):
                    logger.warning("经度数据可能不在中国境内范围")

            except Exception as e:
                return False, f"坐标数据验证失败: {str(e)}"

            return True, f"文件验证通过，使用编码: {used_encoding}"

        except Exception as e:
            logger.error(f"CSV文件验证失败: {e}")
            return False, f"文件验证失败: {str(e)}"

    @staticmethod
    def validate_excel_file(file_path: str) -> Tuple[bool, str]:
        """
        验证Excel文件格式

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            if not Path(file_path).exists():
                return False, "文件不存在"

            # 检查文件大小
            file_size = Path(file_path).stat().st_size
            if file_size == 0:
                return False, "文件为空"

            if file_size > 50 * 1024 * 1024:  # 50MB
                logger.warning(f"Excel文件较大 ({file_size / 1024 / 1024:.1f}MB)，处理可能需要较长时间")

            # 尝试读取Excel文件
            try:
                df = pd.read_excel(file_path, nrows=5)
            except Exception as e:
                return False, f"无法读取Excel文件: {str(e)}"

            # 验证必需列
            required_columns = ['工地名称', '纬度', '经度']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                available_columns = list(df.columns)
                return False, f"缺少必需的列: {', '.join(missing_columns)}\n可用列: {', '.join(available_columns)}"

            # 验证工地名称列
            name_col = '工地名称'
            if df[name_col].isna().all():
                return False, "工地名称列全部为空"

            # 验证坐标数据
            lat_col, lon_col = '纬度', '经度'

            try:
                lat_values = pd.to_numeric(df[lat_col], errors='coerce')
                lon_values = pd.to_numeric(df[lon_col], errors='coerce')

                if lat_values.isna().all() or lon_values.isna().all():
                    return False, "坐标数据全部为空或无效"

                # 检查坐标范围
                valid_lat = lat_values.dropna()
                valid_lon = lon_values.dropna()

                if len(valid_lat) == 0 or len(valid_lon) == 0:
                    return False, "没有有效的坐标数据"

                if not (-90 <= valid_lat.min() <= valid_lat.max() <= 90):
                    return False, "纬度数据超出有效范围 (-90 到 90)"

                if not (-180 <= valid_lon.min() <= valid_lon.max() <= 180):
                    return False, "经度数据超出有效范围 (-180 到 180)"

            except Exception as e:
                return False, f"坐标数据验证失败: {str(e)}"

            return True, "Excel文件验证通过"

        except Exception as e:
            logger.error(f"Excel文件验证失败: {e}")
            return False, f"文件验证失败: {str(e)}"


class DataProcessor:
    """数据处理类 - 负责数据加载、验证和处理"""

    def __init__(self):
        self.track_data: Optional[pd.DataFrame] = None
        self.sites_data: Optional[pd.DataFrame] = None
        self._is_processing: bool = False

    def load_track_data(self, file_path: str) -> pd.DataFrame:
        """
        加载走航数据（增强版本）

        Args:
            file_path: CSV文件路径

        Returns:
            pd.DataFrame: 走航数据

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        # 使用增强的验证功能
        is_valid, message = DataValidator.validate_csv_file(file_path)
        if not is_valid:
            raise ValueError(f"CSV文件验证失败: {message}")

        logger.info(f"CSV文件验证: {message}")

        try:
            # 尝试多种编码格式加载
            encodings = ['gbk', 'utf-8', 'gb2312', 'utf-8-sig']
            self.track_data = None

            for encoding in encodings:
                try:
                    self.track_data = pd.read_csv(file_path, encoding=encoding)
                    logger.info(f"使用编码 {encoding} 成功加载走航数据")
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"使用编码 {encoding} 加载失败: {e}")
                    continue

            if self.track_data is None:
                raise ValueError("无法使用任何编码格式读取文件")

            # 数据清洗和验证
            self.track_data = self._clean_track_data(self.track_data)

            logger.info(f"成功加载走航数据: {len(self.track_data)} 条记录")
            return self.track_data

        except Exception as e:
            logger.error(f"加载走航数据失败: {e}")
            raise ValueError(f"加载走航数据失败: {str(e)}")

    def load_sites_data(self, file_path: str) -> pd.DataFrame:
        """
        加载工地台账数据（增强版本）

        Args:
            file_path: Excel文件路径

        Returns:
            pd.DataFrame: 工地台账数据

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        # 使用增强的验证功能
        is_valid, message = DataValidator.validate_excel_file(file_path)
        if not is_valid:
            raise ValueError(f"Excel文件验证失败: {message}")

        logger.info(f"Excel文件验证: {message}")

        try:
            self.sites_data = pd.read_excel(file_path)

            # 数据清洗和验证
            self.sites_data = self._clean_sites_data(self.sites_data)

            logger.info(f"成功加载工地台账: {len(self.sites_data)} 个工地")
            return self.sites_data

        except Exception as e:
            logger.error(f"加载工地台账失败: {e}")
            raise ValueError(f"加载工地台账失败: {str(e)}")

    def _clean_track_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗走航数据"""
        original_count = len(df)

        # 移除坐标为空的记录
        df = df.dropna(subset=['纬度', '经度'])

        # 转换坐标为数值类型
        df['纬度'] = pd.to_numeric(df['纬度'], errors='coerce')
        df['经度'] = pd.to_numeric(df['经度'], errors='coerce')

        # 移除无效坐标
        df = df.dropna(subset=['纬度', '经度'])

        # 移除明显错误的坐标
        df = df[
            (df['纬度'] >= -90) & (df['纬度'] <= 90) &
            (df['经度'] >= -180) & (df['经度'] <= 180)
        ]

        cleaned_count = len(df)
        if cleaned_count < original_count:
            logger.warning(f"清洗走航数据: 移除了 {original_count - cleaned_count} 条无效记录")

        if cleaned_count == 0:
            raise ValueError("清洗后没有有效的走航数据")

        return df.reset_index(drop=True)

    def _clean_sites_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗工地台账数据"""
        original_count = len(df)

        # 移除工地名称为空的记录
        df = df.dropna(subset=['工地名称'])
        df = df[df['工地名称'].str.strip() != '']

        # 移除坐标为空的记录
        df = df.dropna(subset=['纬度', '经度'])

        # 转换坐标为数值类型
        df['纬度'] = pd.to_numeric(df['纬度'], errors='coerce')
        df['经度'] = pd.to_numeric(df['经度'], errors='coerce')

        # 移除无效坐标
        df = df.dropna(subset=['纬度', '经度'])

        # 移除明显错误的坐标
        df = df[
            (df['纬度'] >= -90) & (df['纬度'] <= 90) &
            (df['经度'] >= -180) & (df['经度'] <= 180)
        ]

        # 移除重复的工地（基于名称和坐标）
        df = df.drop_duplicates(subset=['工地名称', '纬度', '经度'])

        cleaned_count = len(df)
        if cleaned_count < original_count:
            logger.warning(f"清洗工地数据: 移除了 {original_count - cleaned_count} 条无效记录")

        if cleaned_count == 0:
            raise ValueError("清洗后没有有效的工地数据")

        return df.reset_index(drop=True)

    def process_sites(self, radius: float, progress_callback: Optional[Callable[[int, int], None]] = None) -> Optional[pd.DataFrame]:
        """
        处理所有工地数据，匹配走航轨迹（优化版本）

        Args:
            radius: 搜索半径（米）
            progress_callback: 进度回调函数

        Returns:
            Optional[pd.DataFrame]: 匹配结果数据，如果没有匹配则返回None

        Raises:
            ValueError: 数据未加载或处理参数错误
        """
        if self.track_data is None or self.sites_data is None:
            raise ValueError("请先加载数据文件")

        if radius <= 0:
            raise ValueError("搜索半径必须大于0")

        self._is_processing = True

        try:
            # 预处理数据，提取坐标
            track_coords = self.track_data[['纬度', '经度']].values
            site_coords = self.sites_data[['纬度', '经度']].values
            site_names = self.sites_data['工地名称'].values

            total_sites = len(self.sites_data)
            logger.info(f"开始处理 {total_sites} 个工地，{len(track_coords)} 个轨迹点，搜索半径: {radius}米")

            # 使用向量化计算优化性能
            matched_results = self._process_sites_vectorized(
                track_coords, site_coords, site_names, radius, progress_callback
            )

            if not matched_results:
                logger.info("未找到匹配的数据")
                return None

            # 生成最终结果
            final_data = self._generate_final_results(matched_results)
            logger.info(f"处理完成，匹配到 {len(final_data)} 条记录")
            return final_data

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            raise
        finally:
            self._is_processing = False

    def _process_sites_vectorized(self, track_coords: np.ndarray, site_coords: np.ndarray,
                                 site_names: np.ndarray, radius: float,
                                 progress_callback: Optional[Callable[[int, int], None]] = None) -> Dict[str, List[str]]:
        """
        使用向量化计算处理工地匹配（性能优化版本）
        """
        matched_results = {}
        total_sites = len(site_coords)

        # 分批处理以控制内存使用
        batch_size = min(Config.CHUNK_SIZE, total_sites)

        for batch_start in range(0, total_sites, batch_size):
            if not self._is_processing:
                break

            batch_end = min(batch_start + batch_size, total_sites)
            batch_sites = site_coords[batch_start:batch_end]
            batch_names = site_names[batch_start:batch_end]

            # 使用多进程处理当前批次
            batch_results = self._process_batch_multiprocess(
                track_coords, batch_sites, batch_names, radius
            )

            # 合并结果
            for coord_key, names in batch_results.items():
                if coord_key not in matched_results:
                    matched_results[coord_key] = []
                matched_results[coord_key].extend(names)

            # 更新进度
            if progress_callback:
                progress_callback(batch_end - 1, total_sites)

        return matched_results

    def _process_batch_multiprocess(self, track_coords: np.ndarray, site_coords: np.ndarray,
                                   site_names: np.ndarray, radius: float) -> Dict[str, List[str]]:
        """
        使用多进程处理一个批次的工地数据
        """
        batch_results = {}

        # 准备多进程参数
        process_args = [
            (track_coords, site_coords[i], site_names[i], radius, i)
            for i in range(len(site_coords))
        ]

        # 使用多进程池处理
        with Pool(processes=Config.MAX_WORKERS) as pool:
            results = pool.map(self._process_single_site_optimized, process_args)

        # 合并结果
        for result in results:
            if result:
                for coord_key, site_name in result.items():
                    if coord_key not in batch_results:
                        batch_results[coord_key] = []
                    batch_results[coord_key].append(site_name)

        return batch_results

    @staticmethod
    def _process_single_site_optimized(args) -> Dict[str, str]:
        """
        优化的单个工地处理函数（静态方法，支持多进程）
        """
        track_coords, site_coord, site_name, radius, _ = args
        result = {}

        # 使用向量化计算距离
        distances = np.array([
            geodesic((track_coord[0], track_coord[1]), (site_coord[0], site_coord[1])).meters
            for track_coord in track_coords
        ])

        # 找到匹配的轨迹点
        matched_indices = np.where(distances <= radius)[0]

        # 记录匹配结果
        for idx in matched_indices:
            coord_key = f"{track_coords[idx][0]}_{track_coords[idx][1]}"
            result[coord_key] = str(site_name)

        return result

    def _generate_final_results(self, matched_results: Dict[str, List[str]]) -> pd.DataFrame:
        """
        生成最终的匹配结果DataFrame
        """
        if not matched_results:
            return None

        # 创建结果副本
        final_data = self.track_data.copy()

        # 添加匹配工地列
        final_data['匹配工地'] = final_data.apply(
            lambda row: '、'.join(matched_results.get(f"{row['纬度']}_{row['经度']}", [])),
            axis=1
        )

        # 只保留有匹配工地的记录
        final_data = final_data[final_data['匹配工地'] != '']

        # 添加匹配统计信息
        final_data['匹配工地数量'] = final_data['匹配工地'].apply(lambda x: len(x.split('、')) if x else 0)

        return final_data

    def cancel_processing(self) -> None:
        """取消当前处理操作"""
        self._is_processing = False
        logger.info("用户取消了数据处理操作")

def resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径（支持PyInstaller打包）

    Args:
        relative_path: 相对路径

    Returns:
        str: 绝对路径
    """
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except AttributeError:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)


class FileManager:
    """文件管理类"""

    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """获取安全的文件名"""
        import re
        # 移除或替换不安全的字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return safe_name

    @staticmethod
    def ensure_directory(file_path: str) -> None:
        """确保目录存在"""
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

    @staticmethod
    def save_dataframe(df: pd.DataFrame, file_path: str, encoding: str = 'gbk') -> None:
        """
        安全保存DataFrame到CSV文件

        Args:
            df: 要保存的DataFrame
            file_path: 保存路径
            encoding: 编码格式
        """
        FileManager.ensure_directory(file_path)
        df.to_csv(file_path, index=False, encoding=encoding)
        logger.info(f"数据已保存到: {file_path}")

class Application:
    """GUI应用程序主类"""

    def __init__(self, root: tk.Tk):
        """
        初始化应用程序

        Args:
            root: Tkinter根窗口
        """
        self.root = root
        self.data_processor = DataProcessor()
        self.file_manager = FileManager()

        # 界面状态变量
        self.track_file = tk.StringVar()
        self.site_file = tk.StringVar()
        self.radius = tk.StringVar(value=str(Config.DEFAULT_RADIUS))
        self.is_processing = False

        # 界面组件引用
        self.progress: Optional[ttk.Progressbar] = None
        self.status_label: Optional[ttk.Label] = None
        self.process_button: Optional[ttk.Button] = None
        self.cancel_button: Optional[ttk.Button] = None

        self._setup_window()
        self._setup_styles()
        self._create_widgets()
        self._setup_icon()

        logger.info("应用程序初始化完成")

    def _setup_window(self) -> None:
        """设置主窗口属性"""
        self.root.title('工地周边数据匹配工具 v2.0')
        self.root.geometry(Config.WINDOW_SIZE)
        self.root.minsize(*map(int, Config.MIN_WINDOW_SIZE.split('x')))
        self.root.configure(bg=Config.STYLE['bg_color'])

        # 窗口居中
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def _setup_icon(self) -> None:
        """设置程序图标"""
        try:
            icon_path = resource_path("icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
                logger.info("程序图标设置成功")
            else:
                logger.warning("图标文件不存在")
        except Exception as e:
            logger.warning(f"设置程序图标失败: {e}")
        
    def _setup_styles(self) -> None:
        """设置现代化界面样式"""
        style = ttk.Style()

        # 使用默认主题
        try:
            style.theme_use('vista')  # Windows Vista/7+ 主题
        except:
            try:
                style.theme_use('winnative')  # Windows 原生主题
            except:
                style.theme_use('clam')  # 备用主题

        # 定义颜色变量
        primary_color = Config.STYLE['primary_color']
        success_color = Config.STYLE['success_color']
        danger_color = Config.STYLE['danger_color']
        bg_color = Config.STYLE['bg_color']
        font_family = Config.STYLE['font_family']

        # 按钮样式 - 简化版本
        style.configure('Primary.TButton',
                       font=(font_family, Config.STYLE['button_font_size'], 'bold'))

        style.configure('Success.TButton',
                       font=(font_family, Config.STYLE['button_font_size'], 'bold'))

        style.configure('Danger.TButton',
                       font=(font_family, Config.STYLE['button_font_size'], 'bold'))

        # 标签样式
        style.configure('TLabel',
                       font=(font_family, Config.STYLE['normal_font_size']))

        style.configure('Header.TLabel',
                       font=(font_family, Config.STYLE['header_font_size'], 'bold'),
                       foreground=primary_color)

        style.configure('Subtitle.TLabel',
                       font=(font_family, Config.STYLE['normal_font_size']),
                       foreground='#6c757d')

        style.configure('Status.TLabel',
                       font=(font_family, Config.STYLE['normal_font_size']))

        # 输入框样式
        style.configure('TEntry',
                       font=(font_family, Config.STYLE['normal_font_size']))

        # Spinbox样式
        style.configure('TSpinbox',
                       font=(font_family, Config.STYLE['normal_font_size']))

    def _create_widgets(self) -> None:
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding=Config.PADDING)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self._create_header(main_frame)

        # 文件选择区域
        self._create_file_selection(main_frame)

        # 参数设置区域
        self._create_parameter_section(main_frame)

        # 进度显示区域
        self._create_progress_section(main_frame)

        # 操作按钮区域
        self._create_action_buttons(main_frame)

    def _create_header(self, parent: ttk.Frame) -> None:
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = ttk.Label(header_frame,
                               text='🏗️ 工地周边数据匹配工具',
                               style='Header.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(header_frame,
                                  text='智能匹配走航数据与工地台账信息',
                                  style='TLabel')
        subtitle_label.pack(pady=(5, 0))
        
    def _create_file_selection(self, parent: ttk.Frame) -> None:
        """创建现代化文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text='📁 数据文件选择', padding=20)
        file_frame.pack(fill=tk.X, pady=(0, 25))

        # 走航数据文件选择
        track_container = ttk.Frame(file_frame)
        track_container.pack(fill=tk.X, pady=(0, 15))

        # 标签和说明
        track_label_frame = ttk.Frame(track_container)
        track_label_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(track_label_frame, text='🛣️ 走航数据文件',
                 font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size'], 'bold')).pack(side=tk.LEFT)
        ttk.Label(track_label_frame, text='(CSV格式，包含纬度、经度列)',
                 style='Subtitle.TLabel').pack(side=tk.LEFT, padx=(10, 0))

        # 文件选择框
        track_input_frame = ttk.Frame(track_container)
        track_input_frame.pack(fill=tk.X)

        self.track_entry = ttk.Entry(track_input_frame, textvariable=self.track_file,
                                    font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size']))
        self.track_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        track_button = ttk.Button(track_input_frame, text='📂 浏览文件',
                                 command=self._select_track_file,
                                 style='Primary.TButton')
        track_button.pack(side=tk.RIGHT)

        # 文件状态指示器
        self.track_status = ttk.Label(track_container, text='', style='Subtitle.TLabel')
        self.track_status.pack(fill=tk.X, pady=(5, 0))

        # 分隔线
        separator1 = ttk.Separator(file_frame, orient='horizontal')
        separator1.pack(fill=tk.X, pady=(10, 15))

        # 工地台账文件选择
        site_container = ttk.Frame(file_frame)
        site_container.pack(fill=tk.X)

        # 标签和说明
        site_label_frame = ttk.Frame(site_container)
        site_label_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(site_label_frame, text='🏗️ 工地台账文件',
                 font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size'], 'bold')).pack(side=tk.LEFT)
        ttk.Label(site_label_frame, text='(Excel格式，包含工地名称、纬度、经度列)',
                 style='Subtitle.TLabel').pack(side=tk.LEFT, padx=(10, 0))

        # 文件选择框
        site_input_frame = ttk.Frame(site_container)
        site_input_frame.pack(fill=tk.X)

        self.site_entry = ttk.Entry(site_input_frame, textvariable=self.site_file,
                                   font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size']))
        self.site_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        site_button = ttk.Button(site_input_frame, text='📂 浏览文件',
                                command=self._select_site_file,
                                style='Primary.TButton')
        site_button.pack(side=tk.RIGHT)

        # 文件状态指示器
        self.site_status = ttk.Label(site_container, text='', style='Subtitle.TLabel')
        self.site_status.pack(fill=tk.X, pady=(5, 0))

    def _create_parameter_section(self, parent: ttk.Frame) -> None:
        """创建现代化参数设置区域"""
        param_frame = ttk.LabelFrame(parent, text='⚙️ 参数设置', padding=20)
        param_frame.pack(fill=tk.X, pady=(0, 25))

        # 搜索半径设置
        radius_container = ttk.Frame(param_frame)
        radius_container.pack(fill=tk.X)

        # 标签和说明
        radius_label_frame = ttk.Frame(radius_container)
        radius_label_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(radius_label_frame, text='🎯 搜索半径设置',
                 font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size'], 'bold')).pack(side=tk.LEFT)
        ttk.Label(radius_label_frame, text='(设置匹配工地的距离范围)',
                 style='Subtitle.TLabel').pack(side=tk.LEFT, padx=(10, 0))

        # 半径输入区域
        radius_input_frame = ttk.Frame(radius_container)
        radius_input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(radius_input_frame, text='半径 (米):', width=12).pack(side=tk.LEFT)

        self.radius_spinbox = ttk.Spinbox(
            radius_input_frame,
            from_=Config.MIN_RADIUS,
            to=Config.MAX_RADIUS,
            increment=Config.RADIUS_STEP,
            textvariable=self.radius,
            width=15,
            wrap=True,
            command=self._validate_radius,
            font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size'])
        )
        self.radius_spinbox.pack(side=tk.LEFT, padx=(5, 15))

        # 预设半径按钮
        preset_frame = ttk.Frame(radius_input_frame)
        preset_frame.pack(side=tk.LEFT, padx=(10, 0))

        ttk.Label(preset_frame, text='快速设置:', style='Subtitle.TLabel').pack(side=tk.LEFT)

        preset_values = [200, 500, 1000, 1500]
        for value in preset_values:
            btn = ttk.Button(preset_frame, text=f'{value}m', width=6,
                           command=lambda v=value: self._set_radius_preset(v))
            btn.pack(side=tk.LEFT, padx=(5, 2))

        # 半径说明和建议
        info_frame = ttk.Frame(radius_container)
        info_frame.pack(fill=tk.X)

        info_text = f"""
💡 半径设置建议：
• 城市区域：200-500米（建筑密集）
• 郊区区域：500-1000米（建筑稀疏）
• 农村区域：1000-2000米（建筑分散）
• 有效范围：{Config.MIN_RADIUS}-{Config.MAX_RADIUS}米
        """.strip()

        info_label = ttk.Label(info_frame, text=info_text, style='Subtitle.TLabel', justify=tk.LEFT)
        info_label.pack(fill=tk.X, pady=(10, 0))
        
    def _create_progress_section(self, parent: ttk.Frame) -> None:
        """创建现代化进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text='📊 处理进度', padding=20)
        progress_frame.pack(fill=tk.X, pady=(0, 25))

        # 进度信息容器
        progress_info_frame = ttk.Frame(progress_frame)
        progress_info_frame.pack(fill=tk.X, pady=(0, 10))

        # 状态标签
        self.status_label = ttk.Label(progress_info_frame, text='🟢 准备就绪',
                                     style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT)

        # 进度百分比标签
        self.progress_percent_label = ttk.Label(progress_info_frame, text='',
                                               style='Status.TLabel')
        self.progress_percent_label.pack(side=tk.RIGHT)

        # 进度条
        self.progress = ttk.Progressbar(progress_frame, length=500, mode='determinate')
        self.progress.pack(fill=tk.X, pady=(0, 10))

        # 处理统计信息
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill=tk.X)

        self.stats_label = ttk.Label(stats_frame, text='', style='Subtitle.TLabel')
        self.stats_label.pack(fill=tk.X)

    def _create_action_buttons(self, parent: ttk.Frame) -> None:
        """创建操作按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 开始处理按钮
        self.process_button = ttk.Button(
            button_frame,
            text='🚀 开始处理',
            command=self._process_data,
            style='Success.TButton',
            width=15
        )
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))

        # 取消按钮
        self.cancel_button = ttk.Button(
            button_frame,
            text='❌ 取消',
            command=self._cancel_processing,
            style='Danger.TButton',
            width=15,
            state='disabled'
        )
        self.cancel_button.pack(side=tk.LEFT, padx=(10, 0))

        # 帮助按钮
        help_button = ttk.Button(
            button_frame,
            text='❓ 帮助',
            command=self._show_help,
            style='Primary.TButton',
            width=15
        )
        help_button.pack(side=tk.RIGHT)
        
    def _select_track_file(self) -> None:
        """选择走航数据文件"""
        filename = filedialog.askopenfilename(
            title='选择走航数据文件',
            filetypes=[('CSV文件', '*.csv'), ('所有文件', '*.*')],
            initialdir=os.path.expanduser('~')
        )
        if filename:
            self.track_file.set(filename)
            logger.info(f"选择走航数据文件: {filename}")
            self._update_file_status('track', filename)

    def _select_site_file(self) -> None:
        """选择工地台账文件"""
        filename = filedialog.askopenfilename(
            title='选择工地台账文件',
            filetypes=[('Excel文件', '*.xlsx'), ('Excel文件', '*.xls'), ('所有文件', '*.*')],
            initialdir=os.path.expanduser('~')
        )
        if filename:
            self.site_file.set(filename)
            logger.info(f"选择工地台账文件: {filename}")
            self._update_file_status('site', filename)

    def _update_file_status(self, file_type: str, filename: str) -> None:
        """更新文件状态显示"""
        try:
            if file_type == 'track':
                is_valid, message = DataValidator.validate_csv_file(filename)
                status_label = self.track_status
                file_desc = "走航数据文件"
            else:
                is_valid, message = DataValidator.validate_excel_file(filename)
                status_label = self.site_status
                file_desc = "工地台账文件"

            if is_valid:
                status_text = f"✅ {file_desc}验证通过"
                if hasattr(status_label, 'config'):
                    status_label.config(foreground=Config.STYLE['success_color'])
            else:
                status_text = f"❌ {message}"
                if hasattr(status_label, 'config'):
                    status_label.config(foreground=Config.STYLE['danger_color'])

            status_label.config(text=status_text)
            self._update_status(f"已选择{file_desc}: {os.path.basename(filename)}")

        except Exception as e:
            logger.error(f"更新文件状态失败: {e}")

    def _set_radius_preset(self, value: int) -> None:
        """设置预设半径值"""
        self.radius.set(str(value))
        self._validate_radius()
        self._update_status(f"搜索半径已设置为: {value}米")

    def _validate_radius(self) -> None:
        """验证半径输入值"""
        try:
            value = float(self.radius.get())
            if value < Config.MIN_RADIUS:
                self.radius.set(str(Config.MIN_RADIUS))
                self._update_status(f"搜索半径已调整为最小值: {Config.MIN_RADIUS}米", "warning")
            elif value > Config.MAX_RADIUS:
                self.radius.set(str(Config.MAX_RADIUS))
                self._update_status(f"搜索半径已调整为最大值: {Config.MAX_RADIUS}米", "warning")
        except ValueError:
            self.radius.set(str(Config.DEFAULT_RADIUS))
            self._update_status(f"搜索半径已重置为默认值: {Config.DEFAULT_RADIUS}米", "warning")

    def _update_status(self, message: str, status_type: str = "info") -> None:
        """更新状态显示"""
        status_icons = {
            "info": "🟢",
            "warning": "🟡",
            "error": "🔴",
            "processing": "🔄"
        }

        icon = status_icons.get(status_type, "🟢")
        if self.status_label:
            self.status_label.config(text=f"{icon} {message}")
        self.root.update_idletasks()

    def _update_progress(self, current: int, total: int) -> None:
        """更新进度显示（增强版本）"""
        if self.progress and self.status_label:
            progress_percent = (current + 1) / total * 100
            self.progress['value'] = current + 1

            # 更新状态文本
            self._update_status(f"正在处理第 {current+1}/{total} 个工地...", "processing")

            # 更新百分比显示
            if hasattr(self, 'progress_percent_label'):
                self.progress_percent_label.config(text=f"{progress_percent:.1f}%")

            # 更新统计信息
            if hasattr(self, 'stats_label'):
                elapsed_time = getattr(self, '_start_time', None)
                if elapsed_time:
                    elapsed = time.time() - elapsed_time
                    if current > 0:
                        avg_time = elapsed / (current + 1)
                        remaining_time = avg_time * (total - current - 1)
                        stats_text = f"已用时: {elapsed:.1f}秒 | 预计剩余: {remaining_time:.1f}秒"
                        self.stats_label.config(text=stats_text)

            self.root.update_idletasks()
        
    def _process_data(self) -> None:
        """处理数据的主方法"""
        if self.is_processing:
            return

        try:
            # 验证输入
            if not self._validate_inputs():
                return

            self.is_processing = True
            self._set_processing_state(True)

            # 在新线程中处理数据，避免界面冻结
            processing_thread = threading.Thread(target=self._process_data_thread)
            processing_thread.daemon = True
            processing_thread.start()

        except Exception as e:
            logger.error(f"启动数据处理失败: {e}")
            self._update_status(f"启动处理失败: {str(e)}", "error")
            self._set_processing_state(False)

    def _process_data_thread(self) -> None:
        """在后台线程中处理数据"""
        try:
            # 记录开始时间
            self._start_time = time.time()

            # 读取数据
            self._update_status("正在读取数据文件...", "processing")

            self.data_processor.load_track_data(self.track_file.get())
            self.data_processor.load_sites_data(self.site_file.get())

            # 设置进度条
            if self.progress:
                self.progress['maximum'] = len(self.data_processor.sites_data)
                self.progress['value'] = 0

            # 更新统计信息
            if hasattr(self, 'stats_label'):
                track_count = len(self.data_processor.track_data)
                site_count = len(self.data_processor.sites_data)
                self.stats_label.config(text=f"轨迹点: {track_count:,} | 工地数: {site_count:,}")

            # 处理数据
            radius = float(self.radius.get())
            self._update_status(f"开始匹配数据，搜索半径: {radius}米", "processing")

            final_data = self.data_processor.process_sites(
                radius,
                progress_callback=self._update_progress
            )

            # 处理结果
            self.root.after(0, self._handle_processing_result, final_data)

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            self.root.after(0, self._handle_processing_error, str(e))
        finally:
            self.is_processing = False
            self.root.after(0, self._set_processing_state, False)

    def _handle_processing_result(self, final_data: Optional[pd.DataFrame]) -> None:
        """处理数据处理结果"""
        if final_data is not None and len(final_data) > 0:
            self._update_status(f"匹配完成！找到 {len(final_data)} 条匹配记录", "info")
            self._save_results(final_data)
        else:
            self._update_status("未找到匹配数据", "warning")
            messagebox.showwarning('提示', '在指定半径范围内未找到匹配的工地数据！\n\n建议：\n1. 检查数据文件格式是否正确\n2. 尝试增大搜索半径\n3. 确认坐标系统是否一致')

    def _handle_processing_error(self, error_message: str) -> None:
        """处理数据处理错误"""
        self._update_status(f"处理失败: {error_message}", "error")
        messagebox.showerror('错误', f'数据处理失败：\n\n{error_message}\n\n请检查：\n1. 文件格式是否正确\n2. 文件是否包含必要的列\n3. 数据是否完整')

    def _save_results(self, data: pd.DataFrame) -> None:
        """保存处理结果"""
        try:
            # 生成默认文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            default_filename = f"工地匹配结果_{timestamp}.csv"

            save_path = filedialog.asksaveasfilename(
                title='保存匹配结果',
                defaultextension='.csv',
                filetypes=[('CSV文件', '*.csv'), ('所有文件', '*.*')],
                initialfile=default_filename
            )

            if save_path:
                self.file_manager.save_dataframe(data, save_path)
                self._update_status(f"结果已保存: {os.path.basename(save_path)}", "info")

                # 询问是否打开文件所在文件夹
                if messagebox.askyesno('完成', f'数据处理完成！\n\n匹配到 {len(data)} 条记录\n文件已保存至: {save_path}\n\n是否打开文件所在文件夹？'):
                    self._open_file_location(save_path)
            else:
                self._update_status("用户取消保存", "warning")

        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            self._update_status(f"保存失败: {str(e)}", "error")
            messagebox.showerror('错误', f'保存文件失败：\n{str(e)}')

    def _validate_inputs(self) -> bool:
        """验证用户输入"""
        if not self.track_file.get():
            messagebox.showwarning('警告', '请选择走航数据文件！')
            return False

        if not self.site_file.get():
            messagebox.showwarning('警告', '请选择工地台账文件！')
            return False

        if not os.path.exists(self.track_file.get()):
            messagebox.showerror('错误', '走航数据文件不存在！')
            return False

        if not os.path.exists(self.site_file.get()):
            messagebox.showerror('错误', '工地台账文件不存在！')
            return False

        try:
            radius = float(self.radius.get())
            if radius <= 0:
                messagebox.showerror('错误', '搜索半径必须大于0！')
                return False
        except ValueError:
            messagebox.showerror('错误', '搜索半径必须是有效数字！')
            return False

        return True

    def _set_processing_state(self, processing: bool) -> None:
        """设置处理状态"""
        if self.process_button:
            self.process_button.config(state='disabled' if processing else 'normal')
        if self.cancel_button:
            self.cancel_button.config(state='normal' if processing else 'disabled')

    def _cancel_processing(self) -> None:
        """取消数据处理"""
        if self.is_processing:
            self.data_processor.cancel_processing()
            self._update_status("用户取消了处理操作", "warning")
            self._set_processing_state(False)
            self.is_processing = False

    def _open_file_location(self, file_path: str) -> None:
        """打开文件所在位置"""
        try:
            import subprocess
            subprocess.Popen(f'explorer /select,"{file_path}"')
        except Exception as e:
            logger.warning(f"无法打开文件位置: {e}")

    def _show_help(self) -> None:
        """显示帮助信息"""
        help_text = """
🏗️ 工地周边数据匹配工具 - 使用帮助

📋 功能说明：
本工具用于匹配走航数据和工地台账，找出指定半径范围内的工地信息。

📁 数据文件要求：
• 走航数据文件 (CSV格式)：必须包含"纬度"和"经度"列
• 工地台账文件 (Excel格式)：必须包含"工地名称"、"纬度"和"经度"列

⚙️ 参数设置：
• 搜索半径：设置匹配的距离范围（100-2000米）

🚀 操作步骤：
1. 选择走航数据文件和工地台账文件
2. 设置合适的搜索半径
3. 点击"开始处理"按钮
4. 等待处理完成，选择保存位置

💡 注意事项：
• 确保数据文件格式正确
• 坐标系统应保持一致
• 处理大文件时请耐心等待
• 可随时点击"取消"按钮停止处理

📞 技术支持：
如遇问题，请检查日志文件 app.log
        """

        messagebox.showinfo('使用帮助', help_text)


def main():
    """主函数"""
    try:
        # 设置高DPI支持
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        root = tk.Tk()
        app = Application(root)

        logger.info("应用程序启动成功")
        root.mainloop()

    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        messagebox.showerror('启动错误', f'应用程序启动失败：\n{str(e)}')


if __name__ == '__main__':
    main()