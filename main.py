import pandas as pd
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from geopy.distance import geodesic
import os
from multiprocessing import Pool, cpu_count
import sys

class Config:
    """配置类"""
    DEFAULT_RADIUS = 500
    MIN_RADIUS = 100
    MAX_RADIUS = 2000
    RADIUS_STEP = 50
    WINDOW_SIZE = "800x600"
    PADDING = 20
    
    # 样式配置
    STYLE = {
        'bg_color': '#f0f0f0',
        'button_color': '#2196F3',
        'font_family': '微软雅黑',
        'normal_font_size': 10,
        'header_font_size': 12
    }

def calculate_distances(args):
    """计算距离的工具函数"""
    track_coords, site_coords = args
    return geodesic(track_coords, site_coords).meters

class DataProcessor:
    """数据处理类"""
    def __init__(self):
        self.track_data = None
        self.sites_data = None
        
    def load_track_data(self, file_path):
        """加载走航数据"""
        self.track_data = pd.read_csv(file_path, encoding='gbk')
        return self.track_data
        
    def load_sites_data(self, file_path):
        """加载工地台账"""
        self.sites_data = pd.read_excel(file_path)
        return self.sites_data
        
    def process_sites(self, radius, progress_callback=None):
        """处理所有工地数据"""
        if self.track_data is None or self.sites_data is None:
            raise ValueError("请先加载数据文件")
            
        track_coords = list(zip(self.track_data['纬度'], self.track_data['经度']))
        all_matched_data = []
        total_sites = len(self.sites_data)
        
        # 创建一个字典来存储每个坐标点匹配到的工地名称
        point_sites = {}
        
        with Pool(processes=cpu_count()) as pool:
            for idx, site in self.sites_data.iterrows():
                if progress_callback:
                    progress_callback(idx, total_sites)
                    
                site_coords = (site['纬度'], site['经度'])
                calc_args = [(coord, site_coords) for coord in track_coords]
                distances = pool.map(calculate_distances, calc_args)
                
                # 找到匹配的点
                matched_indices = np.where(np.array(distances) <= radius)[0]
                
                # 对每个匹配的点，记录工地名称
                for index in matched_indices:
                    coord_key = f"{track_coords[index][0]}_{track_coords[index][1]}"
                    if coord_key not in point_sites:
                        point_sites[coord_key] = []
                    # 确保工地名称是字符串类型
                    point_sites[coord_key].append(str(site['工地名称']))
        
        # 处理所有匹配的点
        if point_sites:
            matched_data = self.track_data.copy()
            matched_data['速度'] = matched_data.apply(
                lambda row: '、'.join(point_sites.get(f"{row['纬度']}_{row['经度']}", [])),
                axis=1
            )
            # 只保留有匹配工地的数据
            final_data = matched_data[matched_data['速度'] != '']
            return final_data
            
        return None

def resource_path(relative_path):
    """获取资源文件的绝对路径"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class Application:
    """GUI应用类"""
    def __init__(self, root):
        self.root = root
        self.root.title('工地周边数据匹配工具')
        self.root.geometry(Config.WINDOW_SIZE)
        self.root.configure(bg=Config.STYLE['bg_color'])
        
        self.data_processor = DataProcessor()
        self.setup_styles()
        self.create_widgets()
        
        # 设置程序图标
        try:
            icon_path = resource_path("icon.ico")
            self.root.iconbitmap(icon_path)
        except:
            pass  # 如果没有图标文件就忽略
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TButton', 
                       padding=6, 
                       relief='flat', 
                       background=Config.STYLE['button_color'])
        style.configure('TLabel', 
                       background=Config.STYLE['bg_color'], 
                       font=(Config.STYLE['font_family'], 
                       Config.STYLE['normal_font_size']))
        style.configure('Header.TLabel', 
                       font=(Config.STYLE['font_family'], 
                       Config.STYLE['header_font_size'], 
                       'bold'))
        
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding=Config.PADDING)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        header = ttk.Label(main_frame, 
                          text='工地周边数据匹配工具', 
                          style='Header.TLabel')
        header.pack(pady=(0, 20))
        
        # 文件选择区域
        self.create_file_selection(main_frame)
        
        # 参数设置区域
        self.create_parameter_section(main_frame)
        
        # 进度显示区域
        self.create_progress_section(main_frame)
        
    def create_file_selection(self, parent):
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text='数据文件选择', padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 走航数据文件选择
        track_frame = ttk.Frame(file_frame)
        track_frame.pack(fill=tk.X, pady=5)
        ttk.Label(track_frame, text='走航数据文件(CSV):', width=15).pack(side=tk.LEFT)
        self.track_file = tk.StringVar()
        ttk.Entry(track_frame, textvariable=self.track_file, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(track_frame, text='浏览...', command=self.select_track_file).pack(side=tk.LEFT)
        
        # 工地台账文件选择
        site_frame = ttk.Frame(file_frame)
        site_frame.pack(fill=tk.X, pady=5)
        ttk.Label(site_frame, text='工地台账文件(Excel):', width=15).pack(side=tk.LEFT)
        self.site_file = tk.StringVar()
        ttk.Entry(site_frame, textvariable=self.site_file, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(site_frame, text='浏览...', command=self.select_site_file).pack(side=tk.LEFT)
        
    def create_parameter_section(self, parent):
        """创建参数设置区域"""
        param_frame = ttk.LabelFrame(parent, text='参数设置', padding=10)
        param_frame.pack(fill=tk.X, pady=(0, 20))
        
        radius_frame = ttk.Frame(param_frame)
        radius_frame.pack(fill=tk.X, pady=5)
        ttk.Label(radius_frame, text='搜索半径(米):', width=15).pack(side=tk.LEFT)
        
        self.radius = tk.StringVar(value=str(Config.DEFAULT_RADIUS))
        ttk.Spinbox(
            radius_frame,
            from_=Config.MIN_RADIUS,
            to=Config.MAX_RADIUS,
            increment=Config.RADIUS_STEP,
            textvariable=self.radius,
            width=10,
            wrap=True,
            command=self.validate_radius
        ).pack(side=tk.LEFT)
        
    def create_progress_section(self, parent):
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text='处理进度', padding=10)
        progress_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.progress = ttk.Progressbar(progress_frame, length=600, mode='determinate')
        self.progress.pack(pady=10)
        
        self.status = ttk.Label(progress_frame, text='准备就绪')
        self.status.pack()
        
        ttk.Button(
            parent,
            text='开始处理',
            command=self.process_data,
            style='TButton',
            width=20
        ).pack(pady=10)
        
    def select_track_file(self):
        """选择走航数据文件"""
        filename = filedialog.askopenfilename(
            title='选择走航数据文件',
            filetypes=[('CSV文件', '*.csv')]
        )
        self.track_file.set(filename)
        
    def select_site_file(self):
        """选择工地台账文件"""
        filename = filedialog.askopenfilename(
            title='选择工地台账文件',
            filetypes=[('Excel文件', '*.xlsx')]
        )
        self.site_file.set(filename)
        
    def validate_radius(self):
        """验证半径输入值"""
        try:
            value = float(self.radius.get())
            if value < Config.MIN_RADIUS:
                self.radius.set(str(Config.MIN_RADIUS))
            elif value > Config.MAX_RADIUS:
                self.radius.set(str(Config.MAX_RADIUS))
        except ValueError:
            self.radius.set(str(Config.DEFAULT_RADIUS))
            
    def update_progress(self, current, total):
        """更新进度显示"""
        self.status['text'] = f'正在处理第 {current+1}/{total} 个工地...'
        self.progress['value'] = current + 1
        self.root.update()
        
    def process_data(self):
        """处理数据"""
        try:
            if not self.track_file.get() or not self.site_file.get():
                messagebox.showwarning('警告', '请选择数据文件！')
                return
                
            # 读取数据
            self.status['text'] = '正在读取数据...'
            self.root.update()
            
            self.data_processor.load_track_data(self.track_file.get())
            self.data_processor.load_sites_data(self.site_file.get())
            
            # 设置进度条
            self.progress['maximum'] = len(self.data_processor.sites_data)
            self.progress['value'] = 0
            
            # 处理数据
            radius = float(self.radius.get())
            final_data = self.data_processor.process_sites(
                radius, 
                progress_callback=self.update_progress
            )
            
            if final_data is not None:
                # 弹出保存对话框
                save_path = filedialog.asksaveasfilename(
                    title='保存匹配结果',
                    defaultextension='.csv',
                    filetypes=[('CSV文件', '*.csv')],
                    initialfile='matched_data.csv'
                )
                
                if save_path:
                    final_data.to_csv(save_path, index=False, encoding='gbk')
                    self.status['text'] = f'处理完成！结果已保存至: {os.path.basename(save_path)}'
                    messagebox.showinfo('完成', '数据处理已完成！')
                else:
                    self.status['text'] = '已取消保存！'
            else:
                self.status['text'] = '未找到匹配数据！'
                messagebox.showwarning('警告', '未找到匹配数据！')
                
        except Exception as e:
            self.status['text'] = f'错误: {str(e)}'
            messagebox.showerror('错误', str(e))

def main():
    root = tk.Tk()
    app = Application(root)
    root.mainloop()

if __name__ == '__main__':
    main() 