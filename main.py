"""
工地周边数据处理工具
用于匹配走航数据和工地台账，找出指定半径范围内的工地信息

Author: AI Assistant
Version: 2.0.0
Date: 2025-01-29
"""

import pandas as pd
import numpy as np
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from geopy.distance import geodesic
import os
from multiprocessing import Pool, cpu_count
import sys
from typing import Optional, Tuple, List, Callable, Dict, Any
import logging
from pathlib import Path
import threading
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class Config:
    """应用程序配置类"""

    # 数据处理配置
    DEFAULT_RADIUS: int = 500
    MIN_RADIUS: int = 100
    MAX_RADIUS: int = 2000
    RADIUS_STEP: int = 50

    # 界面配置
    WINDOW_SIZE: str = "900x700"
    MIN_WINDOW_SIZE: str = "800x600"
    PADDING: int = 20

    # 样式配置
    STYLE: Dict[str, Any] = {
        'bg_color': '#f8f9fa',
        'primary_color': '#007bff',
        'success_color': '#28a745',
        'warning_color': '#ffc107',
        'danger_color': '#dc3545',
        'font_family': '微软雅黑',
        'normal_font_size': 10,
        'header_font_size': 14,
        'button_font_size': 11
    }

    # 文件配置
    SUPPORTED_CSV_EXTENSIONS: Tuple[str, ...] = ('.csv',)
    SUPPORTED_EXCEL_EXTENSIONS: Tuple[str, ...] = ('.xlsx', '.xls')

    # 性能配置
    MAX_WORKERS: int = min(cpu_count(), 8)  # 限制最大进程数
    CHUNK_SIZE: int = 1000  # 数据块大小

def calculate_distances(args: Tuple[Tuple[float, float], Tuple[float, float]]) -> float:
    """
    计算两个地理坐标点之间的距离

    Args:
        args: 包含两个坐标点的元组 ((lat1, lon1), (lat2, lon2))

    Returns:
        float: 距离（米）
    """
    track_coords, site_coords = args
    return geodesic(track_coords, site_coords).meters


class DataValidator:
    """数据验证类"""

    @staticmethod
    def validate_csv_file(file_path: str) -> bool:
        """验证CSV文件格式"""
        try:
            if not Path(file_path).exists():
                return False

            # 尝试读取文件头部
            df = pd.read_csv(file_path, encoding='gbk', nrows=1)
            required_columns = ['纬度', '经度']

            return all(col in df.columns for col in required_columns)
        except Exception as e:
            logger.error(f"CSV文件验证失败: {e}")
            return False

    @staticmethod
    def validate_excel_file(file_path: str) -> bool:
        """验证Excel文件格式"""
        try:
            if not Path(file_path).exists():
                return False

            # 尝试读取文件头部
            df = pd.read_excel(file_path, nrows=1)
            required_columns = ['工地名称', '纬度', '经度']

            return all(col in df.columns for col in required_columns)
        except Exception as e:
            logger.error(f"Excel文件验证失败: {e}")
            return False


class DataProcessor:
    """数据处理类 - 负责数据加载、验证和处理"""

    def __init__(self):
        self.track_data: Optional[pd.DataFrame] = None
        self.sites_data: Optional[pd.DataFrame] = None
        self._is_processing: bool = False

    def load_track_data(self, file_path: str) -> pd.DataFrame:
        """
        加载走航数据

        Args:
            file_path: CSV文件路径

        Returns:
            pd.DataFrame: 走航数据

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        if not DataValidator.validate_csv_file(file_path):
            raise ValueError("CSV文件格式不正确，请确保包含'纬度'和'经度'列")

        try:
            self.track_data = pd.read_csv(file_path, encoding='gbk')
            logger.info(f"成功加载走航数据: {len(self.track_data)} 条记录")
            return self.track_data
        except Exception as e:
            logger.error(f"加载走航数据失败: {e}")
            raise ValueError(f"加载走航数据失败: {str(e)}")

    def load_sites_data(self, file_path: str) -> pd.DataFrame:
        """
        加载工地台账数据

        Args:
            file_path: Excel文件路径

        Returns:
            pd.DataFrame: 工地台账数据

        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        if not DataValidator.validate_excel_file(file_path):
            raise ValueError("Excel文件格式不正确，请确保包含'工地名称'、'纬度'和'经度'列")

        try:
            self.sites_data = pd.read_excel(file_path)
            logger.info(f"成功加载工地台账: {len(self.sites_data)} 个工地")
            return self.sites_data
        except Exception as e:
            logger.error(f"加载工地台账失败: {e}")
            raise ValueError(f"加载工地台账失败: {str(e)}")

    def process_sites(self, radius: float, progress_callback: Optional[Callable[[int, int], None]] = None) -> Optional[pd.DataFrame]:
        """
        处理所有工地数据，匹配走航轨迹

        Args:
            radius: 搜索半径（米）
            progress_callback: 进度回调函数

        Returns:
            Optional[pd.DataFrame]: 匹配结果数据，如果没有匹配则返回None

        Raises:
            ValueError: 数据未加载或处理参数错误
        """
        if self.track_data is None or self.sites_data is None:
            raise ValueError("请先加载数据文件")

        if radius <= 0:
            raise ValueError("搜索半径必须大于0")

        self._is_processing = True

        try:
            track_coords = list(zip(self.track_data['纬度'], self.track_data['经度']))
            total_sites = len(self.sites_data)
            point_sites = {}

            logger.info(f"开始处理 {total_sites} 个工地，搜索半径: {radius}米")

            # 使用优化的多进程处理
            with Pool(processes=Config.MAX_WORKERS) as pool:
                for idx, site in self.sites_data.iterrows():
                    if not self._is_processing:  # 支持取消操作
                        break

                    if progress_callback:
                        progress_callback(idx, total_sites)

                    site_coords = (site['纬度'], site['经度'])
                    calc_args = [(coord, site_coords) for coord in track_coords]

                    # 批量计算距离
                    distances = pool.map(calculate_distances, calc_args)

                    # 找到匹配的点
                    matched_indices = np.where(np.array(distances) <= radius)[0]

                    # 记录匹配的工地名称
                    for index in matched_indices:
                        coord_key = f"{track_coords[index][0]}_{track_coords[index][1]}"
                        if coord_key not in point_sites:
                            point_sites[coord_key] = []
                        point_sites[coord_key].append(str(site['工地名称']))

            # 生成最终结果
            if point_sites:
                matched_data = self.track_data.copy()
                matched_data['匹配工地'] = matched_data.apply(
                    lambda row: '、'.join(point_sites.get(f"{row['纬度']}_{row['经度']}", [])),
                    axis=1
                )
                final_data = matched_data[matched_data['匹配工地'] != '']

                logger.info(f"处理完成，匹配到 {len(final_data)} 条记录")
                return final_data

            logger.info("未找到匹配的数据")
            return None

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            raise
        finally:
            self._is_processing = False

    def cancel_processing(self) -> None:
        """取消当前处理操作"""
        self._is_processing = False
        logger.info("用户取消了数据处理操作")

def resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径（支持PyInstaller打包）

    Args:
        relative_path: 相对路径

    Returns:
        str: 绝对路径
    """
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except AttributeError:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)


class FileManager:
    """文件管理类"""

    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """获取安全的文件名"""
        import re
        # 移除或替换不安全的字符
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return safe_name

    @staticmethod
    def ensure_directory(file_path: str) -> None:
        """确保目录存在"""
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)

    @staticmethod
    def save_dataframe(df: pd.DataFrame, file_path: str, encoding: str = 'gbk') -> None:
        """
        安全保存DataFrame到CSV文件

        Args:
            df: 要保存的DataFrame
            file_path: 保存路径
            encoding: 编码格式
        """
        FileManager.ensure_directory(file_path)
        df.to_csv(file_path, index=False, encoding=encoding)
        logger.info(f"数据已保存到: {file_path}")

class Application:
    """GUI应用程序主类"""

    def __init__(self, root: tk.Tk):
        """
        初始化应用程序

        Args:
            root: Tkinter根窗口
        """
        self.root = root
        self.data_processor = DataProcessor()
        self.file_manager = FileManager()

        # 界面状态变量
        self.track_file = tk.StringVar()
        self.site_file = tk.StringVar()
        self.radius = tk.StringVar(value=str(Config.DEFAULT_RADIUS))
        self.is_processing = False

        # 界面组件引用
        self.progress: Optional[ttk.Progressbar] = None
        self.status_label: Optional[ttk.Label] = None
        self.process_button: Optional[ttk.Button] = None
        self.cancel_button: Optional[ttk.Button] = None

        self._setup_window()
        self._setup_styles()
        self._create_widgets()
        self._setup_icon()

        logger.info("应用程序初始化完成")

    def _setup_window(self) -> None:
        """设置主窗口属性"""
        self.root.title('工地周边数据匹配工具 v2.0')
        self.root.geometry(Config.WINDOW_SIZE)
        self.root.minsize(*map(int, Config.MIN_WINDOW_SIZE.split('x')))
        self.root.configure(bg=Config.STYLE['bg_color'])

        # 窗口居中
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def _setup_icon(self) -> None:
        """设置程序图标"""
        try:
            icon_path = resource_path("icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
                logger.info("程序图标设置成功")
            else:
                logger.warning("图标文件不存在")
        except Exception as e:
            logger.warning(f"设置程序图标失败: {e}")
        
    def _setup_styles(self) -> None:
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 按钮样式
        style.configure('Primary.TButton',
                       padding=(12, 8),
                       relief='flat',
                       font=(Config.STYLE['font_family'], Config.STYLE['button_font_size']))

        style.configure('Success.TButton',
                       padding=(12, 8),
                       relief='flat',
                       font=(Config.STYLE['font_family'], Config.STYLE['button_font_size']))

        style.configure('Danger.TButton',
                       padding=(12, 8),
                       relief='flat',
                       font=(Config.STYLE['font_family'], Config.STYLE['button_font_size']))

        # 标签样式
        style.configure('TLabel',
                       background=Config.STYLE['bg_color'],
                       font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size']))

        style.configure('Header.TLabel',
                       background=Config.STYLE['bg_color'],
                       font=(Config.STYLE['font_family'], Config.STYLE['header_font_size'], 'bold'),
                       foreground=Config.STYLE['primary_color'])

        style.configure('Status.TLabel',
                       background=Config.STYLE['bg_color'],
                       font=(Config.STYLE['font_family'], Config.STYLE['normal_font_size']))

        # 框架样式
        style.configure('Card.TLabelFrame',
                       background=Config.STYLE['bg_color'],
                       relief='solid',
                       borderwidth=1)

    def _create_widgets(self) -> None:
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root, padding=Config.PADDING)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self._create_header(main_frame)

        # 文件选择区域
        self._create_file_selection(main_frame)

        # 参数设置区域
        self._create_parameter_section(main_frame)

        # 进度显示区域
        self._create_progress_section(main_frame)

        # 操作按钮区域
        self._create_action_buttons(main_frame)

    def _create_header(self, parent: ttk.Frame) -> None:
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 30))

        title_label = ttk.Label(header_frame,
                               text='🏗️ 工地周边数据匹配工具',
                               style='Header.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(header_frame,
                                  text='智能匹配走航数据与工地台账信息',
                                  style='TLabel')
        subtitle_label.pack(pady=(5, 0))
        
    def _create_file_selection(self, parent: ttk.Frame) -> None:
        """创建文件选择区域"""
        file_frame = ttk.LabelFrame(parent, text='📁 数据文件选择',
                                   padding=15, style='Card.TLabelFrame')
        file_frame.pack(fill=tk.X, pady=(0, 20))

        # 走航数据文件选择
        track_frame = ttk.Frame(file_frame)
        track_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(track_frame, text='走航数据文件 (CSV):', width=18).pack(side=tk.LEFT)
        track_entry = ttk.Entry(track_frame, textvariable=self.track_file, width=45)
        track_entry.pack(side=tk.LEFT, padx=(5, 10), fill=tk.X, expand=True)
        ttk.Button(track_frame, text='浏览...',
                  command=self._select_track_file,
                  style='Primary.TButton').pack(side=tk.RIGHT)

        # 工地台账文件选择
        site_frame = ttk.Frame(file_frame)
        site_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(site_frame, text='工地台账文件 (Excel):', width=18).pack(side=tk.LEFT)
        site_entry = ttk.Entry(site_frame, textvariable=self.site_file, width=45)
        site_entry.pack(side=tk.LEFT, padx=(5, 10), fill=tk.X, expand=True)
        ttk.Button(site_frame, text='浏览...',
                  command=self._select_site_file,
                  style='Primary.TButton').pack(side=tk.RIGHT)

    def _create_parameter_section(self, parent: ttk.Frame) -> None:
        """创建参数设置区域"""
        param_frame = ttk.LabelFrame(parent, text='⚙️ 参数设置',
                                    padding=15, style='Card.TLabelFrame')
        param_frame.pack(fill=tk.X, pady=(0, 20))

        # 搜索半径设置
        radius_frame = ttk.Frame(param_frame)
        radius_frame.pack(fill=tk.X, pady=5)

        ttk.Label(radius_frame, text='搜索半径 (米):', width=18).pack(side=tk.LEFT)

        radius_spinbox = ttk.Spinbox(
            radius_frame,
            from_=Config.MIN_RADIUS,
            to=Config.MAX_RADIUS,
            increment=Config.RADIUS_STEP,
            textvariable=self.radius,
            width=12,
            wrap=True,
            command=self._validate_radius
        )
        radius_spinbox.pack(side=tk.LEFT, padx=(5, 10))

        # 半径说明
        radius_info = ttk.Label(radius_frame,
                               text=f'范围: {Config.MIN_RADIUS}-{Config.MAX_RADIUS}米',
                               foreground='gray')
        radius_info.pack(side=tk.LEFT, padx=(10, 0))
        
    def _create_progress_section(self, parent: ttk.Frame) -> None:
        """创建进度显示区域"""
        progress_frame = ttk.LabelFrame(parent, text='📊 处理进度',
                                       padding=15, style='Card.TLabelFrame')
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        # 进度条
        self.progress = ttk.Progressbar(progress_frame, length=500, mode='determinate')
        self.progress.pack(pady=(0, 10), fill=tk.X)

        # 状态标签
        self.status_label = ttk.Label(progress_frame, text='🟢 准备就绪',
                                     style='Status.TLabel')
        self.status_label.pack()

    def _create_action_buttons(self, parent: ttk.Frame) -> None:
        """创建操作按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 开始处理按钮
        self.process_button = ttk.Button(
            button_frame,
            text='🚀 开始处理',
            command=self._process_data,
            style='Success.TButton',
            width=15
        )
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))

        # 取消按钮
        self.cancel_button = ttk.Button(
            button_frame,
            text='❌ 取消',
            command=self._cancel_processing,
            style='Danger.TButton',
            width=15,
            state='disabled'
        )
        self.cancel_button.pack(side=tk.LEFT, padx=(10, 0))

        # 帮助按钮
        help_button = ttk.Button(
            button_frame,
            text='❓ 帮助',
            command=self._show_help,
            style='Primary.TButton',
            width=15
        )
        help_button.pack(side=tk.RIGHT)
        
    def _select_track_file(self) -> None:
        """选择走航数据文件"""
        filename = filedialog.askopenfilename(
            title='选择走航数据文件',
            filetypes=[('CSV文件', '*.csv'), ('所有文件', '*.*')],
            initialdir=os.path.expanduser('~')
        )
        if filename:
            self.track_file.set(filename)
            logger.info(f"选择走航数据文件: {filename}")
            self._update_status(f"已选择走航数据文件: {os.path.basename(filename)}")

    def _select_site_file(self) -> None:
        """选择工地台账文件"""
        filename = filedialog.askopenfilename(
            title='选择工地台账文件',
            filetypes=[('Excel文件', '*.xlsx'), ('Excel文件', '*.xls'), ('所有文件', '*.*')],
            initialdir=os.path.expanduser('~')
        )
        if filename:
            self.site_file.set(filename)
            logger.info(f"选择工地台账文件: {filename}")
            self._update_status(f"已选择工地台账文件: {os.path.basename(filename)}")

    def _validate_radius(self) -> None:
        """验证半径输入值"""
        try:
            value = float(self.radius.get())
            if value < Config.MIN_RADIUS:
                self.radius.set(str(Config.MIN_RADIUS))
                self._update_status(f"搜索半径已调整为最小值: {Config.MIN_RADIUS}米", "warning")
            elif value > Config.MAX_RADIUS:
                self.radius.set(str(Config.MAX_RADIUS))
                self._update_status(f"搜索半径已调整为最大值: {Config.MAX_RADIUS}米", "warning")
        except ValueError:
            self.radius.set(str(Config.DEFAULT_RADIUS))
            self._update_status(f"搜索半径已重置为默认值: {Config.DEFAULT_RADIUS}米", "warning")

    def _update_status(self, message: str, status_type: str = "info") -> None:
        """更新状态显示"""
        status_icons = {
            "info": "🟢",
            "warning": "🟡",
            "error": "🔴",
            "processing": "🔄"
        }

        icon = status_icons.get(status_type, "🟢")
        if self.status_label:
            self.status_label.config(text=f"{icon} {message}")
        self.root.update_idletasks()

    def _update_progress(self, current: int, total: int) -> None:
        """更新进度显示"""
        if self.progress and self.status_label:
            progress_percent = (current + 1) / total * 100
            self.progress['value'] = current + 1
            self._update_status(f"正在处理第 {current+1}/{total} 个工地... ({progress_percent:.1f}%)", "processing")
            self.root.update_idletasks()
        
    def process_data(self):
        """处理数据"""
        try:
            if not self.track_file.get() or not self.site_file.get():
                messagebox.showwarning('警告', '请选择数据文件！')
                return
                
            # 读取数据
            self.status['text'] = '正在读取数据...'
            self.root.update()
            
            self.data_processor.load_track_data(self.track_file.get())
            self.data_processor.load_sites_data(self.site_file.get())
            
            # 设置进度条
            self.progress['maximum'] = len(self.data_processor.sites_data)
            self.progress['value'] = 0
            
            # 处理数据
            radius = float(self.radius.get())
            final_data = self.data_processor.process_sites(
                radius, 
                progress_callback=self.update_progress
            )
            
            if final_data is not None:
                # 弹出保存对话框
                save_path = filedialog.asksaveasfilename(
                    title='保存匹配结果',
                    defaultextension='.csv',
                    filetypes=[('CSV文件', '*.csv')],
                    initialfile='matched_data.csv'
                )
                
                if save_path:
                    final_data.to_csv(save_path, index=False, encoding='gbk')
                    self.status['text'] = f'处理完成！结果已保存至: {os.path.basename(save_path)}'
                    messagebox.showinfo('完成', '数据处理已完成！')
                else:
                    self.status['text'] = '已取消保存！'
            else:
                self.status['text'] = '未找到匹配数据！'
                messagebox.showwarning('警告', '未找到匹配数据！')
                
        except Exception as e:
            self.status['text'] = f'错误: {str(e)}'
            messagebox.showerror('错误', str(e))

def main():
    root = tk.Tk()
    app = Application(root)
    root.mainloop()

if __name__ == '__main__':
    main() 