2025-07-29 11:58:20,785 - ERROR - 应用程序启动失败: Layout Card.TLabelFrame not found
2025-07-29 12:02:39,706 - INFO - 程序图标设置成功
2025-07-29 12:02:39,706 - INFO - 应用程序初始化完成
2025-07-29 12:02:39,706 - INFO - 应用程序启动成功
2025-07-29 12:08:35,614 - INFO - 程序图标设置成功
2025-07-29 12:08:35,615 - INFO - 应用程序初始化完成
2025-07-29 12:08:35,615 - INFO - 应用程序启动成功
2025-07-29 12:09:46,791 - INFO - 程序图标设置成功
2025-07-29 12:09:46,792 - INFO - 应用程序初始化完成
2025-07-29 12:09:46,792 - INFO - 应用程序启动成功
2025-07-29 12:20:01,536 - INFO - 程序图标设置成功
2025-07-29 12:20:01,536 - INFO - 应用程序初始化完成
2025-07-29 12:20:01,538 - INFO - 应用程序启动成功
2025-07-29 12:23:42,405 - INFO - 程序图标设置成功
2025-07-29 12:23:42,406 - INFO - 应用程序初始化完成
2025-07-29 12:23:42,406 - INFO - 应用程序启动成功
2025-07-29 12:24:23,754 - INFO - 程序图标设置成功
2025-07-29 12:24:23,755 - INFO - 应用程序初始化完成
2025-07-29 12:24:23,755 - INFO - 应用程序启动成功
2025-07-29 12:26:07,478 - INFO - 程序图标设置成功
2025-07-29 12:26:07,479 - INFO - 应用程序初始化完成
2025-07-29 12:26:07,479 - INFO - 应用程序启动成功
2025-07-29 12:26:15,917 - INFO - 选择走航数据文件: C:/Users/<USER>/Desktop/经开区7月月报/20250726经开区工地.csv
2025-07-29 12:26:15,925 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:26:32,977 - INFO - 选择工地台账文件: C:/Users/<USER>/Desktop/经开区7月月报/经开区工地07月工地台账.xlsx
2025-07-29 12:26:34,856 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:26:34,857 - INFO - CSV文件验证: 文件验证通过，使用编码: gbk
2025-07-29 12:26:34,877 - INFO - 使用编码 gbk 成功加载走航数据
2025-07-29 12:26:34,883 - INFO - 成功加载走航数据: 8397 条记录
2025-07-29 12:26:34,890 - INFO - Excel文件验证: Excel文件验证通过
2025-07-29 12:26:34,898 - ERROR - 加载工地台账失败: Can only use .str accessor with string values!
2025-07-29 12:26:34,898 - ERROR - 数据处理失败: 加载工地台账失败: Can only use .str accessor with string values!
2025-07-29 12:28:18,305 - INFO - 程序图标设置成功
2025-07-29 12:28:18,305 - INFO - 应用程序初始化完成
2025-07-29 12:28:18,306 - INFO - 应用程序启动成功
2025-07-29 12:28:25,870 - INFO - 选择走航数据文件: C:/Users/<USER>/Desktop/经开区7月月报/20250726经开区工地.csv
2025-07-29 12:28:25,877 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:28:34,119 - INFO - 选择工地台账文件: C:/Users/<USER>/Desktop/经开区7月月报/经开区工地07月工地台账.xlsx
2025-07-29 12:28:34,388 - INFO - 工地名称列数据类型: int64
2025-07-29 12:28:34,389 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:28:34,391 - INFO - 纬度列数据类型: float64
2025-07-29 12:28:34,391 - INFO - 经度列数据类型: float64
2025-07-29 12:28:34,393 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:28:36,388 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:28:36,389 - INFO - CSV文件验证: 文件验证通过，使用编码: gbk
2025-07-29 12:28:36,408 - INFO - 使用编码 gbk 成功加载走航数据
2025-07-29 12:28:36,414 - INFO - 成功加载走航数据: 8397 条记录
2025-07-29 12:28:36,421 - INFO - 工地名称列数据类型: int64
2025-07-29 12:28:36,421 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:28:36,422 - INFO - 纬度列数据类型: float64
2025-07-29 12:28:36,422 - INFO - 经度列数据类型: float64
2025-07-29 12:28:36,423 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:28:36,423 - INFO - Excel文件验证: Excel文件验证通过 (共 10 行数据)
2025-07-29 12:28:36,433 - INFO - 工地数据清洗完成: 保留 37 条有效记录
2025-07-29 12:28:36,433 - INFO - 成功加载工地台账: 37 个工地
2025-07-29 12:28:36,439 - INFO - 开始处理 37 个工地，8397 个轨迹点，搜索半径: 500.0米
2025-07-29 12:28:45,673 - INFO - 处理完成，匹配到 6528 条记录
2025-07-29 12:28:51,412 - INFO - 数据已保存到: C:/Users/<USER>/Desktop/工地匹配结果_20250729_122845.csv
2025-07-29 12:29:36,798 - INFO - 程序图标设置成功
2025-07-29 12:29:36,798 - INFO - 应用程序初始化完成
2025-07-29 12:29:36,798 - INFO - 应用程序启动成功
2025-07-29 12:29:52,511 - INFO - 选择走航数据文件: C:/Users/<USER>/Desktop/经开区7月月报/20250726经开区工地.csv
2025-07-29 12:29:52,520 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:29:59,493 - INFO - 选择工地台账文件: C:/Users/<USER>/Desktop/经开区7月月报/经开区工地07月工地台账.xlsx
2025-07-29 12:29:59,733 - INFO - 工地名称列数据类型: int64
2025-07-29 12:29:59,733 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:29:59,733 - INFO - 纬度列数据类型: float64
2025-07-29 12:29:59,733 - INFO - 经度列数据类型: float64
2025-07-29 12:29:59,734 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:30:04,476 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:30:04,477 - INFO - CSV文件验证: 文件验证通过，使用编码: gbk
2025-07-29 12:30:04,496 - INFO - 使用编码 gbk 成功加载走航数据
2025-07-29 12:30:04,500 - INFO - 成功加载走航数据: 8397 条记录
2025-07-29 12:30:04,506 - INFO - 工地名称列数据类型: int64
2025-07-29 12:30:04,506 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:30:04,507 - INFO - 纬度列数据类型: float64
2025-07-29 12:30:04,507 - INFO - 经度列数据类型: float64
2025-07-29 12:30:04,507 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:30:04,507 - INFO - Excel文件验证: Excel文件验证通过 (共 10 行数据)
2025-07-29 12:30:04,517 - INFO - 工地数据清洗完成: 保留 37 条有效记录
2025-07-29 12:30:04,517 - INFO - 成功加载工地台账: 37 个工地
2025-07-29 12:30:04,521 - INFO - 开始处理 37 个工地，8397 个轨迹点，搜索半径: 250.0米
2025-07-29 12:30:07,155 - INFO - 程序图标设置成功
2025-07-29 12:30:07,155 - INFO - 应用程序初始化完成
2025-07-29 12:30:07,155 - INFO - 应用程序启动成功
2025-07-29 12:30:09,458 - INFO - 程序图标设置成功
2025-07-29 12:30:09,458 - INFO - 应用程序初始化完成
2025-07-29 12:30:09,458 - INFO - 应用程序启动成功
2025-07-29 12:30:12,254 - INFO - 程序图标设置成功
2025-07-29 12:30:12,254 - INFO - 应用程序初始化完成
2025-07-29 12:30:12,254 - INFO - 应用程序启动成功
2025-07-29 12:30:13,122 - INFO - 程序图标设置成功
2025-07-29 12:30:13,123 - INFO - 应用程序初始化完成
2025-07-29 12:30:13,123 - INFO - 应用程序启动成功
2025-07-29 12:30:13,145 - INFO - 程序图标设置成功
2025-07-29 12:30:13,145 - INFO - 应用程序初始化完成
2025-07-29 12:30:13,145 - INFO - 应用程序启动成功
2025-07-29 12:30:13,145 - INFO - 应用程序初始化完成
2025-07-29 12:30:13,145 - INFO - 应用程序启动成功
2025-07-29 12:30:13,211 - INFO - 程序图标设置成功
2025-07-29 12:30:13,211 - INFO - 应用程序初始化完成
2025-07-29 12:30:13,211 - INFO - 应用程序启动成功
2025-07-29 12:30:13,266 - INFO - 程序图标设置成功
2025-07-29 12:30:13,266 - INFO - 应用程序初始化完成
2025-07-29 12:30:13,266 - INFO - 应用程序启动成功
2025-07-29 12:30:22,125 - INFO - 程序图标设置成功
2025-07-29 12:30:22,125 - INFO - 应用程序初始化完成
2025-07-29 12:30:22,126 - INFO - 应用程序启动成功
2025-07-29 12:30:24,672 - INFO - 程序图标设置成功
2025-07-29 12:30:24,673 - INFO - 应用程序初始化完成
2025-07-29 12:30:24,673 - INFO - 应用程序启动成功
2025-07-29 12:30:26,597 - INFO - 程序图标设置成功
2025-07-29 12:30:26,597 - INFO - 应用程序初始化完成
2025-07-29 12:30:26,597 - INFO - 应用程序启动成功
2025-07-29 12:30:26,899 - INFO - 程序图标设置成功
2025-07-29 12:30:26,899 - INFO - 应用程序初始化完成
2025-07-29 12:30:26,899 - INFO - 应用程序启动成功
2025-07-29 12:30:26,899 - INFO - 程序图标设置成功
2025-07-29 12:30:26,900 - INFO - 应用程序初始化完成
2025-07-29 12:30:26,900 - INFO - 应用程序启动成功
2025-07-29 12:30:26,900 - INFO - 程序图标设置成功
2025-07-29 12:30:26,900 - INFO - 应用程序初始化完成
2025-07-29 12:30:26,901 - INFO - 应用程序启动成功
2025-07-29 12:48:32,842 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:48:32,842 - INFO - 成功设置主题: vista
2025-07-29 12:48:32,852 - INFO - 样式配置完成
2025-07-29 12:48:32,971 - INFO - 程序图标设置成功
2025-07-29 12:48:32,971 - INFO - 应用程序初始化完成
2025-07-29 12:48:32,971 - INFO - 应用程序启动成功
2025-07-29 12:48:56,087 - INFO - 选择走航数据文件: C:/Users/<USER>/Desktop/经开区7月月报/20250726经开区工地.csv
2025-07-29 12:48:56,093 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:49:06,790 - INFO - 选择工地台账文件: C:/Users/<USER>/Desktop/经开区7月月报/经开区工地07月工地台账.xlsx
2025-07-29 12:49:06,980 - INFO - 工地名称列数据类型: int64
2025-07-29 12:49:06,980 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:49:06,981 - INFO - 纬度列数据类型: float64
2025-07-29 12:49:06,981 - INFO - 经度列数据类型: float64
2025-07-29 12:49:06,981 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:49:08,852 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:49:08,852 - INFO - CSV文件验证: 文件验证通过，使用编码: gbk
2025-07-29 12:49:08,868 - INFO - 使用编码 gbk 成功加载走航数据
2025-07-29 12:49:08,871 - INFO - 成功加载走航数据: 8397 条记录
2025-07-29 12:49:08,877 - INFO - 工地名称列数据类型: int64
2025-07-29 12:49:08,877 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:49:08,878 - INFO - 纬度列数据类型: float64
2025-07-29 12:49:08,878 - INFO - 经度列数据类型: float64
2025-07-29 12:49:08,878 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:49:08,878 - INFO - Excel文件验证: Excel文件验证通过 (共 10 行数据)
2025-07-29 12:49:08,886 - INFO - 工地数据清洗完成: 保留 37 条有效记录
2025-07-29 12:49:08,886 - INFO - 成功加载工地台账: 37 个工地
2025-07-29 12:49:08,894 - INFO - 开始处理 37 个工地，8397 个轨迹点，搜索半径: 500.0米
2025-07-29 12:49:09,790 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:09,791 - INFO - 成功设置主题: vista
2025-07-29 12:49:09,799 - INFO - 样式配置完成
2025-07-29 12:49:10,005 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:10,005 - INFO - 成功设置主题: vista
2025-07-29 12:49:10,018 - INFO - 样式配置完成
2025-07-29 12:49:10,248 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:10,267 - INFO - 成功设置主题: vista
2025-07-29 12:49:10,295 - INFO - 程序图标设置成功
2025-07-29 12:49:10,295 - INFO - 应用程序初始化完成
2025-07-29 12:49:10,295 - INFO - 应用程序启动成功
2025-07-29 12:49:10,339 - INFO - 样式配置完成
2025-07-29 12:49:10,561 - INFO - 程序图标设置成功
2025-07-29 12:49:10,561 - INFO - 应用程序初始化完成
2025-07-29 12:49:10,561 - INFO - 应用程序启动成功
2025-07-29 12:49:10,567 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:10,579 - INFO - 成功设置主题: vista
2025-07-29 12:49:10,680 - INFO - 样式配置完成
2025-07-29 12:49:10,836 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:10,838 - INFO - 成功设置主题: vista
2025-07-29 12:49:10,900 - INFO - 样式配置完成
2025-07-29 12:49:10,903 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:10,905 - INFO - 成功设置主题: vista
2025-07-29 12:49:10,959 - INFO - 样式配置完成
2025-07-29 12:49:10,963 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:10,963 - INFO - 成功设置主题: vista
2025-07-29 12:49:11,017 - INFO - 样式配置完成
2025-07-29 12:49:11,021 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:11,022 - INFO - 成功设置主题: vista
2025-07-29 12:49:11,051 - INFO - 样式配置完成
2025-07-29 12:49:11,084 - INFO - 程序图标设置成功
2025-07-29 12:49:11,084 - INFO - 应用程序初始化完成
2025-07-29 12:49:11,084 - INFO - 应用程序启动成功
2025-07-29 12:49:11,224 - INFO - 程序图标设置成功
2025-07-29 12:49:11,224 - INFO - 应用程序初始化完成
2025-07-29 12:49:11,224 - INFO - 应用程序启动成功
2025-07-29 12:49:11,314 - INFO - 程序图标设置成功
2025-07-29 12:49:11,314 - INFO - 应用程序初始化完成
2025-07-29 12:49:11,314 - INFO - 应用程序启动成功
2025-07-29 12:49:11,352 - INFO - 程序图标设置成功
2025-07-29 12:49:11,352 - INFO - 应用程序初始化完成
2025-07-29 12:49:11,352 - INFO - 应用程序启动成功
2025-07-29 12:49:11,393 - INFO - 程序图标设置成功
2025-07-29 12:49:11,393 - INFO - 应用程序初始化完成
2025-07-29 12:49:11,393 - INFO - 应用程序启动成功
2025-07-29 12:49:11,399 - INFO - 程序图标设置成功
2025-07-29 12:49:11,399 - INFO - 应用程序初始化完成
2025-07-29 12:49:11,399 - INFO - 应用程序启动成功
2025-07-29 12:49:16,229 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:16,230 - INFO - 成功设置主题: vista
2025-07-29 12:49:16,237 - INFO - 样式配置完成
2025-07-29 12:49:16,305 - INFO - 程序图标设置成功
2025-07-29 12:49:16,305 - INFO - 应用程序初始化完成
2025-07-29 12:49:16,305 - INFO - 应用程序启动成功
2025-07-29 12:49:17,102 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:17,103 - INFO - 成功设置主题: vista
2025-07-29 12:49:17,111 - INFO - 样式配置完成
2025-07-29 12:49:17,158 - INFO - 程序图标设置成功
2025-07-29 12:49:17,158 - INFO - 应用程序初始化完成
2025-07-29 12:49:17,158 - INFO - 应用程序启动成功
2025-07-29 12:49:18,451 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:18,451 - INFO - 成功设置主题: vista
2025-07-29 12:49:18,465 - INFO - 样式配置完成
2025-07-29 12:49:18,577 - INFO - 程序图标设置成功
2025-07-29 12:49:18,577 - INFO - 应用程序初始化完成
2025-07-29 12:49:18,577 - INFO - 应用程序启动成功
2025-07-29 12:49:18,992 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:18,994 - INFO - 成功设置主题: vista
2025-07-29 12:49:19,035 - INFO - 样式配置完成
2025-07-29 12:49:19,281 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:19,282 - INFO - 成功设置主题: vista
2025-07-29 12:49:19,372 - INFO - 样式配置完成
2025-07-29 12:49:19,409 - INFO - 程序图标设置成功
2025-07-29 12:49:19,409 - INFO - 应用程序初始化完成
2025-07-29 12:49:19,409 - INFO - 应用程序启动成功
2025-07-29 12:49:19,526 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:19,528 - INFO - 成功设置主题: vista
2025-07-29 12:49:19,568 - INFO - 样式配置完成
2025-07-29 12:49:19,848 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:19,849 - INFO - 成功设置主题: vista
2025-07-29 12:49:19,878 - INFO - 样式配置完成
2025-07-29 12:49:19,912 - INFO - 程序图标设置成功
2025-07-29 12:49:19,912 - INFO - 应用程序初始化完成
2025-07-29 12:49:19,912 - INFO - 应用程序启动成功
2025-07-29 12:49:19,953 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:19,954 - INFO - 成功设置主题: vista
2025-07-29 12:49:19,987 - INFO - 样式配置完成
2025-07-29 12:49:20,021 - INFO - 程序图标设置成功
2025-07-29 12:49:20,022 - INFO - 应用程序初始化完成
2025-07-29 12:49:20,022 - INFO - 应用程序启动成功
2025-07-29 12:49:20,216 - INFO - 程序图标设置成功
2025-07-29 12:49:20,216 - INFO - 应用程序初始化完成
2025-07-29 12:49:20,216 - INFO - 应用程序启动成功
2025-07-29 12:49:20,350 - INFO - 程序图标设置成功
2025-07-29 12:49:20,350 - INFO - 应用程序初始化完成
2025-07-29 12:49:20,350 - INFO - 应用程序启动成功
2025-07-29 12:49:20,422 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:20,423 - INFO - 成功设置主题: vista
2025-07-29 12:49:20,450 - INFO - 样式配置完成
2025-07-29 12:49:20,730 - INFO - 程序图标设置成功
2025-07-29 12:49:20,730 - INFO - 应用程序初始化完成
2025-07-29 12:49:20,730 - INFO - 应用程序启动成功
2025-07-29 12:49:21,036 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:21,036 - INFO - 成功设置主题: vista
2025-07-29 12:49:21,053 - INFO - 样式配置完成
2025-07-29 12:49:21,164 - INFO - 程序图标设置成功
2025-07-29 12:49:21,164 - INFO - 应用程序初始化完成
2025-07-29 12:49:21,164 - INFO - 应用程序启动成功
2025-07-29 12:49:37,656 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:37,656 - INFO - 成功设置主题: vista
2025-07-29 12:49:37,664 - INFO - 样式配置完成
2025-07-29 12:49:37,695 - INFO - 程序图标设置成功
2025-07-29 12:49:37,696 - INFO - 应用程序初始化完成
2025-07-29 12:49:37,696 - INFO - 应用程序启动成功
2025-07-29 12:49:42,865 - INFO - 可用TTK主题: ('winnative', 'clam', 'alt', 'default', 'classic', 'vista', 'xpnative')
2025-07-29 12:49:42,866 - INFO - 成功设置主题: vista
2025-07-29 12:49:42,876 - INFO - 样式配置完成
2025-07-29 12:49:42,912 - INFO - 程序图标设置成功
2025-07-29 12:49:42,913 - INFO - 应用程序初始化完成
2025-07-29 12:49:42,913 - INFO - 应用程序启动成功
2025-07-29 12:49:54,948 - INFO - 程序图标设置成功
2025-07-29 12:49:54,948 - INFO - 应用程序初始化完成
2025-07-29 12:49:54,948 - INFO - 应用程序启动成功
2025-07-29 12:52:21,078 - INFO - 程序图标设置成功
2025-07-29 12:52:21,078 - INFO - 应用程序初始化完成
2025-07-29 12:52:21,079 - INFO - 应用程序启动成功
2025-07-29 12:52:49,778 - INFO - 选择走航数据文件: C:/Users/<USER>/Desktop/经开区7月月报/20250726经开区工地.csv
2025-07-29 12:52:49,783 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:52:55,462 - INFO - 选择工地台账文件: C:/Users/<USER>/Desktop/经开区7月月报/经开区工地07月工地台账.xlsx
2025-07-29 12:52:55,677 - INFO - 工地名称列数据类型: int64
2025-07-29 12:52:55,677 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:52:55,678 - INFO - 纬度列数据类型: float64
2025-07-29 12:52:55,678 - INFO - 经度列数据类型: float64
2025-07-29 12:52:55,679 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:52:57,099 - INFO - 使用编码 gbk 成功读取CSV文件
2025-07-29 12:52:57,100 - INFO - CSV文件验证: 文件验证通过，使用编码: gbk
2025-07-29 12:52:57,116 - INFO - 使用编码 gbk 成功加载走航数据
2025-07-29 12:52:57,120 - INFO - 成功加载走航数据: 8397 条记录
2025-07-29 12:52:57,126 - INFO - 工地名称列数据类型: int64
2025-07-29 12:52:57,127 - INFO - 工地名称列前5个值: [1, 2, 3, 4, 5]
2025-07-29 12:52:57,127 - INFO - 纬度列数据类型: float64
2025-07-29 12:52:57,127 - INFO - 经度列数据类型: float64
2025-07-29 12:52:57,128 - INFO - Excel文件验证: 总计 10 行，有效数据 10 行
2025-07-29 12:52:57,128 - INFO - Excel文件验证: Excel文件验证通过 (共 10 行数据)
2025-07-29 12:52:57,135 - INFO - 工地数据清洗完成: 保留 37 条有效记录
2025-07-29 12:52:57,136 - INFO - 成功加载工地台账: 37 个工地
2025-07-29 12:52:57,143 - INFO - 开始处理 37 个工地，8397 个轨迹点，搜索半径: 200.0米
2025-07-29 12:53:02,262 - INFO - 处理完成，匹配到 2434 条记录
2025-07-29 12:53:06,798 - INFO - 数据已保存到: C:/Users/<USER>/Desktop/工地匹配结果_20250729_125302.csv
