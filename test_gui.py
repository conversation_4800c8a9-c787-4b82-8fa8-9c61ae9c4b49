#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速GUI测试
"""

import tkinter as tk
from tkinter import ttk
import sys

def test_basic_gui():
    """测试基本GUI功能"""
    print("测试基本GUI功能...")
    
    try:
        root = tk.Tk()
        root.title("GUI测试")
        root.geometry("400x300")
        
        # 测试TTK样式
        style = ttk.Style()
        print(f"可用主题: {style.theme_names()}")
        
        # 尝试设置主题
        for theme in ['vista', 'winnative', 'clam']:
            try:
                style.theme_use(theme)
                print(f"成功设置主题: {theme}")
                break
            except:
                continue
        
        # 创建基本组件
        ttk.Label(root, text="GUI测试成功！", font=('Arial', 14)).pack(pady=20)
        
        frame = ttk.LabelFrame(root, text="测试框架", padding=10)
        frame.pack(pady=10, padx=10, fill=tk.X)
        
        ttk.Label(frame, text="标签测试").pack()
        ttk.Button(frame, text="按钮测试").pack(pady=5)
        ttk.Entry(frame).pack(pady=5)
        ttk.Progressbar(frame, length=200).pack(pady=5)
        
        ttk.Button(root, text="关闭", command=root.destroy).pack(pady=20)
        
        print("✅ GUI组件创建成功")
        
        # 自动关闭
        root.after(5000, root.destroy)
        root.mainloop()
        
        print("✅ GUI测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🔍 快速GUI测试")
    print("=" * 30)
    
    if test_basic_gui():
        print("\n🎉 GUI功能正常！")
    else:
        print("\n❌ GUI存在问题！")
    
    input("按回车键退出...")

if __name__ == '__main__':
    main()
