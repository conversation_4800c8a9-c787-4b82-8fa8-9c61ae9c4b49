(['E:\\cursor_project\\方便截图软件\\main.py'],
 ['E:\\cursor_project\\方便截图软件', 'E:\\cursor_project\\方便截图软件'],
 ['http',
  'http.client',
  'http.server',
  'http.cookies',
  'urllib',
  'urllib.request',
  'urllib.parse',
  'urllib.error',
  'pandas._libs.tslibs.timedeltas',
  'pandas._libs.tslibs.np_datetime',
  'pandas._libs.tslibs.nattype',
  'pandas._libs.properties',
  'pandas._libs.testing',
  'geopy.distance',
  'geopy.geocoders',
  'openpyxl.cell._writer',
  'openpyxl.workbook.external_link.external',
  'multiprocessing.pool',
  'multiprocessing.util',
  'tkinter.ttk',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'ctypes.wintypes',
  'pkg_resources',
  'pkg_resources._vendor',
  'pkg_resources.extern',
  'email',
  'email.mime',
  'email.mime.text',
  'json',
  'xml',
  'xml.etree',
  'xml.etree.ElementTree'],
 [('E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['pytest',
  'unittest',
  'test',
  'IPython',
  'jupyter',
  'notebook',
  'PyQt5',
  'PyQt6',
  'PySide2',
  'PySide6',
  'wx',
  'matplotlib',
  'scipy',
  'sklearn',
  'tensorflow',
  'torch',
  'requests',
  'urllib3',
  'doctest',
  'pdb',
  'profile',
  'pstats',
  '__main__'],
 [],
 False,
 {},
 2,
 [],
 [('icon.ico', 'E:\\cursor_project\\方便截图软件\\icon.ico', 'DATA')],
 '3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'E:\\cursor_project\\方便截图软件\\main.py', 'PYSOURCE-2')],
 [('multiprocessing.spawn',
   'C:\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'C:\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('signal', 'C:\\Python313\\Lib\\signal.py', 'PYMODULE-2'),
  ('multiprocessing.context',
   'C:\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'C:\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'C:\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('struct', 'C:\\Python313\\Lib\\struct.py', 'PYMODULE-2'),
  ('socket', 'C:\\Python313\\Lib\\socket.py', 'PYMODULE-2'),
  ('selectors', 'C:\\Python313\\Lib\\selectors.py', 'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'C:\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'C:\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('tempfile', 'C:\\Python313\\Lib\\tempfile.py', 'PYMODULE-2'),
  ('random', 'C:\\Python313\\Lib\\random.py', 'PYMODULE-2'),
  ('argparse', 'C:\\Python313\\Lib\\argparse.py', 'PYMODULE-2'),
  ('textwrap', 'C:\\Python313\\Lib\\textwrap.py', 'PYMODULE-2'),
  ('copy', 'C:\\Python313\\Lib\\copy.py', 'PYMODULE-2'),
  ('gettext', 'C:\\Python313\\Lib\\gettext.py', 'PYMODULE-2'),
  ('statistics', 'C:\\Python313\\Lib\\statistics.py', 'PYMODULE-2'),
  ('decimal', 'C:\\Python313\\Lib\\decimal.py', 'PYMODULE-2'),
  ('_pydecimal', 'C:\\Python313\\Lib\\_pydecimal.py', 'PYMODULE-2'),
  ('contextvars', 'C:\\Python313\\Lib\\contextvars.py', 'PYMODULE-2'),
  ('fractions', 'C:\\Python313\\Lib\\fractions.py', 'PYMODULE-2'),
  ('numbers', 'C:\\Python313\\Lib\\numbers.py', 'PYMODULE-2'),
  ('hashlib', 'C:\\Python313\\Lib\\hashlib.py', 'PYMODULE-2'),
  ('shutil', 'C:\\Python313\\Lib\\shutil.py', 'PYMODULE-2'),
  ('zipfile', 'C:\\Python313\\Lib\\zipfile\\__init__.py', 'PYMODULE-2'),
  ('zipfile._path',
   'C:\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'C:\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('contextlib', 'C:\\Python313\\Lib\\contextlib.py', 'PYMODULE-2'),
  ('py_compile', 'C:\\Python313\\Lib\\py_compile.py', 'PYMODULE-2'),
  ('importlib.machinery',
   'C:\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib', 'C:\\Python313\\Lib\\importlib\\__init__.py', 'PYMODULE-2'),
  ('importlib._bootstrap',
   'C:\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'C:\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'C:\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('csv', 'C:\\Python313\\Lib\\csv.py', 'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'C:\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'C:\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('email.message', 'C:\\Python313\\Lib\\email\\message.py', 'PYMODULE-2'),
  ('email.policy', 'C:\\Python313\\Lib\\email\\policy.py', 'PYMODULE-2'),
  ('email.contentmanager',
   'C:\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'C:\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('string', 'C:\\Python313\\Lib\\string.py', 'PYMODULE-2'),
  ('email.headerregistry',
   'C:\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'C:\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email.iterators', 'C:\\Python313\\Lib\\email\\iterators.py', 'PYMODULE-2'),
  ('email.generator', 'C:\\Python313\\Lib\\email\\generator.py', 'PYMODULE-2'),
  ('email._encoded_words',
   'C:\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('base64', 'C:\\Python313\\Lib\\base64.py', 'PYMODULE-2'),
  ('getopt', 'C:\\Python313\\Lib\\getopt.py', 'PYMODULE-2'),
  ('email.charset', 'C:\\Python313\\Lib\\email\\charset.py', 'PYMODULE-2'),
  ('email.encoders', 'C:\\Python313\\Lib\\email\\encoders.py', 'PYMODULE-2'),
  ('email.base64mime',
   'C:\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email._policybase',
   'C:\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.header', 'C:\\Python313\\Lib\\email\\header.py', 'PYMODULE-2'),
  ('email.errors', 'C:\\Python313\\Lib\\email\\errors.py', 'PYMODULE-2'),
  ('email.utils', 'C:\\Python313\\Lib\\email\\utils.py', 'PYMODULE-2'),
  ('email._parseaddr',
   'C:\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('calendar', 'C:\\Python313\\Lib\\calendar.py', 'PYMODULE-2'),
  ('datetime', 'C:\\Python313\\Lib\\datetime.py', 'PYMODULE-2'),
  ('_pydatetime', 'C:\\Python313\\Lib\\_pydatetime.py', 'PYMODULE-2'),
  ('_strptime', 'C:\\Python313\\Lib\\_strptime.py', 'PYMODULE-2'),
  ('quopri', 'C:\\Python313\\Lib\\quopri.py', 'PYMODULE-2'),
  ('importlib.abc', 'C:\\Python313\\Lib\\importlib\\abc.py', 'PYMODULE-2'),
  ('importlib.resources.abc',
   'C:\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'C:\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._functional',
   'C:\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'C:\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'C:\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('importlib._abc', 'C:\\Python313\\Lib\\importlib\\_abc.py', 'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'C:\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'C:\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'C:\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('inspect', 'C:\\Python313\\Lib\\inspect.py', 'PYMODULE-2'),
  ('token', 'C:\\Python313\\Lib\\token.py', 'PYMODULE-2'),
  ('dis', 'C:\\Python313\\Lib\\dis.py', 'PYMODULE-2'),
  ('opcode', 'C:\\Python313\\Lib\\opcode.py', 'PYMODULE-2'),
  ('_opcode_metadata', 'C:\\Python313\\Lib\\_opcode_metadata.py', 'PYMODULE-2'),
  ('ast', 'C:\\Python313\\Lib\\ast.py', 'PYMODULE-2'),
  ('__future__', 'C:\\Python313\\Lib\\__future__.py', 'PYMODULE-2'),
  ('importlib.readers',
   'C:\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'C:\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'C:\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('tokenize', 'C:\\Python313\\Lib\\tokenize.py', 'PYMODULE-2'),
  ('importlib.util', 'C:\\Python313\\Lib\\importlib\\util.py', 'PYMODULE-2'),
  ('tarfile', 'C:\\Python313\\Lib\\tarfile.py', 'PYMODULE-2'),
  ('gzip', 'C:\\Python313\\Lib\\gzip.py', 'PYMODULE-2'),
  ('_compression', 'C:\\Python313\\Lib\\_compression.py', 'PYMODULE-2'),
  ('lzma', 'C:\\Python313\\Lib\\lzma.py', 'PYMODULE-2'),
  ('bz2', 'C:\\Python313\\Lib\\bz2.py', 'PYMODULE-2'),
  ('fnmatch', 'C:\\Python313\\Lib\\fnmatch.py', 'PYMODULE-2'),
  ('bisect', 'C:\\Python313\\Lib\\bisect.py', 'PYMODULE-2'),
  ('multiprocessing.queues',
   'C:\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('queue', 'C:\\Python313\\Lib\\queue.py', 'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'C:\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'C:\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'C:\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('xmlrpc.client', 'C:\\Python313\\Lib\\xmlrpc\\client.py', 'PYMODULE-2'),
  ('xmlrpc', 'C:\\Python313\\Lib\\xmlrpc\\__init__.py', 'PYMODULE-2'),
  ('xml.parsers.expat',
   'C:\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'C:\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('hmac', 'C:\\Python313\\Lib\\hmac.py', 'PYMODULE-2'),
  ('multiprocessing.managers',
   'C:\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'C:\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('secrets', 'C:\\Python313\\Lib\\secrets.py', 'PYMODULE-2'),
  ('multiprocessing.reduction',
   'C:\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('pickle', 'C:\\Python313\\Lib\\pickle.py', 'PYMODULE-2'),
  ('pprint', 'C:\\Python313\\Lib\\pprint.py', 'PYMODULE-2'),
  ('dataclasses', 'C:\\Python313\\Lib\\dataclasses.py', 'PYMODULE-2'),
  ('_compat_pickle', 'C:\\Python313\\Lib\\_compat_pickle.py', 'PYMODULE-2'),
  ('multiprocessing.process',
   'C:\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('runpy', 'C:\\Python313\\Lib\\runpy.py', 'PYMODULE-2'),
  ('pkgutil', 'C:\\Python313\\Lib\\pkgutil.py', 'PYMODULE-2'),
  ('zipimport', 'C:\\Python313\\Lib\\zipimport.py', 'PYMODULE-2'),
  ('_distutils_hack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE-2'),
  ('setuptools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.msvc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE-2'),
  ('setuptools._vendor.typing_extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE-2'),
  ('setuptools._vendor', '-', 'PYMODULE-2'),
  ('setuptools._vendor.more_itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.more_itertools.recipes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE-2'),
  ('setuptools._vendor.more_itertools.more',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE-2'),
  ('platform', 'C:\\Python313\\Lib\\platform.py', 'PYMODULE-2'),
  ('_ios_support', 'C:\\Python313\\Lib\\_ios_support.py', 'PYMODULE-2'),
  ('ctypes.util', 'C:\\Python313\\Lib\\ctypes\\util.py', 'PYMODULE-2'),
  ('ctypes._aix', 'C:\\Python313\\Lib\\ctypes\\_aix.py', 'PYMODULE-2'),
  ('ctypes.macholib.dyld',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE-2'),
  ('ctypes.macholib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE-2'),
  ('ctypes.macholib.dylib',
   'C:\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE-2'),
  ('ctypes.macholib.framework',
   'C:\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.build_ext',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE-2'),
  ('setuptools._distutils.archive_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.spawn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE-2'),
  ('setuptools._distutils.debug',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE-2'),
  ('setuptools._distutils.dir_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.file_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils._msvccompiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers.C.msvc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers.C.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers.C.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE-2'),
  ('setuptools._distutils.fancy_getopt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE-2'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE-2'),
  ('concurrent.futures',
   'C:\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'C:\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'C:\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'C:\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent', 'C:\\Python313\\Lib\\concurrent\\__init__.py', 'PYMODULE-2'),
  ('setuptools._distutils.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE-2'),
  ('jaraco', '-', 'PYMODULE-2'),
  ('setuptools._vendor.jaraco.functools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE-2'),
  ('sysconfig', 'C:\\Python313\\Lib\\sysconfig\\__init__.py', 'PYMODULE-2'),
  ('_aix_support', 'C:\\Python313\\Lib\\_aix_support.py', 'PYMODULE-2'),
  ('setuptools._distutils.sysconfig',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE-2'),
  ('setuptools._distutils.text_file',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE-2'),
  ('setuptools._distutils.ccompiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE-2'),
  ('setuptools._distutils.compat.numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE-2'),
  ('setuptools._distutils._modified',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE-2'),
  ('site', 'C:\\Python313\\Lib\\site.py', 'PYMODULE-2'),
  ('_pyrepl.main', 'C:\\Python313\\Lib\\_pyrepl\\main.py', 'PYMODULE-2'),
  ('_pyrepl', 'C:\\Python313\\Lib\\_pyrepl\\__init__.py', 'PYMODULE-2'),
  ('_pyrepl.curses', 'C:\\Python313\\Lib\\_pyrepl\\curses.py', 'PYMODULE-2'),
  ('curses', 'C:\\Python313\\Lib\\curses\\__init__.py', 'PYMODULE-2'),
  ('curses.has_key', 'C:\\Python313\\Lib\\curses\\has_key.py', 'PYMODULE-2'),
  ('_pyrepl._minimal_curses',
   'C:\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE-2'),
  ('_pyrepl.input', 'C:\\Python313\\Lib\\_pyrepl\\input.py', 'PYMODULE-2'),
  ('_pyrepl.keymap', 'C:\\Python313\\Lib\\_pyrepl\\keymap.py', 'PYMODULE-2'),
  ('_pyrepl.types', 'C:\\Python313\\Lib\\_pyrepl\\types.py', 'PYMODULE-2'),
  ('_pyrepl.commands',
   'C:\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE-2'),
  ('_pyrepl.pager', 'C:\\Python313\\Lib\\_pyrepl\\pager.py', 'PYMODULE-2'),
  ('tty', 'C:\\Python313\\Lib\\tty.py', 'PYMODULE-2'),
  ('_pyrepl.historical_reader',
   'C:\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE-2'),
  ('_pyrepl.reader', 'C:\\Python313\\Lib\\_pyrepl\\reader.py', 'PYMODULE-2'),
  ('_pyrepl._threading_handler',
   'C:\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE-2'),
  ('_pyrepl.utils', 'C:\\Python313\\Lib\\_pyrepl\\utils.py', 'PYMODULE-2'),
  ('_colorize', 'C:\\Python313\\Lib\\_colorize.py', 'PYMODULE-2'),
  ('_pyrepl.console', 'C:\\Python313\\Lib\\_pyrepl\\console.py', 'PYMODULE-2'),
  ('code', 'C:\\Python313\\Lib\\code.py', 'PYMODULE-2'),
  ('codeop', 'C:\\Python313\\Lib\\codeop.py', 'PYMODULE-2'),
  ('_pyrepl.trace', 'C:\\Python313\\Lib\\_pyrepl\\trace.py', 'PYMODULE-2'),
  ('_pyrepl.simple_interact',
   'C:\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE-2'),
  ('_pyrepl.unix_console',
   'C:\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE-2'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE-2'),
  ('_pyrepl.base_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\base_eventqueue.py',
   'PYMODULE-2'),
  ('_pyrepl.fancy_termios',
   'C:\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE-2'),
  ('_pyrepl.windows_console',
   'C:\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE-2'),
  ('_pyrepl.windows_eventqueue',
   'C:\\Python313\\Lib\\_pyrepl\\windows_eventqueue.py',
   'PYMODULE-2'),
  ('_pyrepl.readline',
   'C:\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE-2'),
  ('_pyrepl.completing_reader',
   'C:\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE-2'),
  ('rlcompleter', 'C:\\Python313\\Lib\\rlcompleter.py', 'PYMODULE-2'),
  ('_sitebuiltins', 'C:\\Python313\\Lib\\_sitebuiltins.py', 'PYMODULE-2'),
  ('pydoc', 'C:\\Python313\\Lib\\pydoc.py', 'PYMODULE-2'),
  ('webbrowser', 'C:\\Python313\\Lib\\webbrowser.py', 'PYMODULE-2'),
  ('shlex', 'C:\\Python313\\Lib\\shlex.py', 'PYMODULE-2'),
  ('pydoc_data.topics',
   'C:\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pydoc_data', 'C:\\Python313\\Lib\\pydoc_data\\__init__.py', 'PYMODULE-2'),
  ('setuptools._distutils._log',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE-2'),
  ('setuptools._distutils.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE-2'),
  ('setuptools._distutils.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE-2'),
  ('setuptools._distutils.dist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE-2'),
  ('setuptools._distutils.versionpredicate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE-2'),
  ('configparser', 'C:\\Python313\\Lib\\configparser.py', 'PYMODULE-2'),
  ('packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE-2'),
  ('packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE-2'),
  ('packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE-2'),
  ('packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE-2'),
  ('packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE-2'),
  ('packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE-2'),
  ('setuptools._distutils.cmd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE-2'),
  ('setuptools.warnings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE-2'),
  ('setuptools.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE-2'),
  ('setuptools._importlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._functools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._collections',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.importlib_metadata._meta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp.glob',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp.compat.py310',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE-2'),
  ('setuptools._vendor.zipp.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE-2'),
  ('setuptools._path',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE-2'),
  ('setuptools.dist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE-2'),
  ('setuptools.command.bdist_wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.command.egg_info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE-2'),
  ('setuptools._distutils.filelist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE-2'),
  ('setuptools.command._requirestxt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE-2'),
  ('setuptools._vendor.jaraco.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.jaraco.context',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports.tarfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE-2'),
  ('setuptools._vendor.backports.tarfile.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE-2'),
  ('backports',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.glob',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE-2'),
  ('setuptools.command.setopt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE-2'),
  ('setuptools.command.sdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.sdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE-2'),
  ('setuptools.command.build',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.build',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE-2'),
  ('setuptools.command.bdist_egg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE-2'),
  ('setuptools.unicode_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE-2'),
  ('setuptools.compat.py39',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE-2'),
  ('setuptools.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.compat.py311',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE-2'),
  ('packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE-2'),
  ('packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE-2'),
  ('packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.wheelfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE-2'),
  ('email.parser', 'C:\\Python313\\Lib\\email\\parser.py', 'PYMODULE-2'),
  ('email.feedparser',
   'C:\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.convert',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.pack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE-2'),
  ('setuptools._vendor.wheel.cli.unpack',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE-2'),
  ('setuptools.installer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE-2'),
  ('setuptools.wheel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE-2'),
  ('setuptools.archive_util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE-2'),
  ('setuptools._distutils.log',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE-2'),
  ('setuptools.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE-2'),
  ('setuptools.config.setupcfg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE-2'),
  ('setuptools.config.expand',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE-2'),
  ('setuptools.config.pyprojecttoml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE-2'),
  ('setuptools.config._validate_pyproject.formats',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE-2'),
  ('packaging.licenses',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE-2'),
  ('packaging.licenses._spdx',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE-2'),
  ('ssl', 'C:\\Python313\\Lib\\ssl.py', 'PYMODULE-2'),
  ('setuptools._vendor.packaging.requirements',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._structures',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._musllinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._elffile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._manylinux',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('setuptools.compat.py310',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli._types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE-2'),
  ('setuptools._vendor.tomli._re',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE-2'),
  ('tomllib', 'C:\\Python313\\Lib\\tomllib\\__init__.py', 'PYMODULE-2'),
  ('tomllib._parser', 'C:\\Python313\\Lib\\tomllib\\_parser.py', 'PYMODULE-2'),
  ('tomllib._types', 'C:\\Python313\\Lib\\tomllib\\_types.py', 'PYMODULE-2'),
  ('tomllib._re', 'C:\\Python313\\Lib\\tomllib\\_re.py', 'PYMODULE-2'),
  ('setuptools.config._apply_pyprojecttoml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE-2'),
  ('setuptools.config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._static',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE-2'),
  ('packaging.specifiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE-2'),
  ('packaging.markers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE-2'),
  ('glob', 'C:\\Python313\\Lib\\glob.py', 'PYMODULE-2'),
  ('setuptools._shutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE-2'),
  ('setuptools.windows_support',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE-2'),
  ('setuptools.command',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._distutils.command.bdist',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE-2'),
  ('setuptools._entry_points',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE-2'),
  ('setuptools._itertools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE-2'),
  ('setuptools.discovery',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE-2'),
  ('setuptools.depends',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE-2'),
  ('setuptools._imp',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE-2'),
  ('setuptools.logging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE-2'),
  ('setuptools.monkey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE-2'),
  ('setuptools._core_metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE-2'),
  ('setuptools._reqs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE-2'),
  ('setuptools._normalization',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE-2'),
  ('_distutils_hack.override',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'C:\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'C:\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'C:\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree', 'C:\\Python313\\Lib\\xml\\etree\\__init__.py', 'PYMODULE-2'),
  ('xml', 'C:\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE-2'),
  ('xml.sax.expatreader',
   'C:\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'C:\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax', 'C:\\Python313\\Lib\\xml\\sax\\__init__.py', 'PYMODULE-2'),
  ('xml.sax.handler', 'C:\\Python313\\Lib\\xml\\sax\\handler.py', 'PYMODULE-2'),
  ('xml.sax._exceptions',
   'C:\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'C:\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('json', 'C:\\Python313\\Lib\\json\\__init__.py', 'PYMODULE-2'),
  ('json.encoder', 'C:\\Python313\\Lib\\json\\encoder.py', 'PYMODULE-2'),
  ('json.decoder', 'C:\\Python313\\Lib\\json\\decoder.py', 'PYMODULE-2'),
  ('json.scanner', 'C:\\Python313\\Lib\\json\\scanner.py', 'PYMODULE-2'),
  ('email.mime.text', 'C:\\Python313\\Lib\\email\\mime\\text.py', 'PYMODULE-2'),
  ('email.mime.nonmultipart',
   'C:\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE-2'),
  ('email.mime.base', 'C:\\Python313\\Lib\\email\\mime\\base.py', 'PYMODULE-2'),
  ('email.mime', 'C:\\Python313\\Lib\\email\\mime\\__init__.py', 'PYMODULE-2'),
  ('email', 'C:\\Python313\\Lib\\email\\__init__.py', 'PYMODULE-2'),
  ('pkg_resources',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE-2'),
  ('packaging.metadata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.android',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.unix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.macos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.windows',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE-2'),
  ('setuptools._vendor.platformdirs.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE-2'),
  ('plistlib', 'C:\\Python313\\Lib\\plistlib.py', 'PYMODULE-2'),
  ('ctypes.wintypes', 'C:\\Python313\\Lib\\ctypes\\wintypes.py', 'PYMODULE-2'),
  ('multiprocessing.util',
   'C:\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'C:\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'C:\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link.external',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.utils.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.child',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.header_footer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.utils.escape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.custom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.packaging',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.defined_name',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-2'),
  ('openpyxl.utils.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.formula',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formula.tokenizer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.chartsheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.publish',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.custom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.styles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.styles.numbers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.relation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.page',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.drawing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.relation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.picture',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.effect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.colors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.chart.shapes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-2'),
  ('openpyxl.chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series_factory',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-2'),
  ('openpyxl.chart.trendline',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-2'),
  ('openpyxl.chart.layout',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-2'),
  ('openpyxl.chart.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart.marker',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-2'),
  ('openpyxl.chart.picture',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.chart.label',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-2'),
  ('openpyxl.chart.error_bar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-2'),
  ('openpyxl.chart.data_source',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-2'),
  ('openpyxl.chart.surface_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.axis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-2'),
  ('openpyxl.chart.title',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-2'),
  ('openpyxl.chart.descriptors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-2'),
  ('openpyxl.chart._3d',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-2'),
  ('openpyxl.chart.stock_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.updown_bars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-2'),
  ('openpyxl.chart.scatter_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.radar_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pie_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.line_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bubble_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bar_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.legend',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-2'),
  ('openpyxl.chart.area_chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.line',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.geometry',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.graphic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.connector',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.fill',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.xdr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-2'),
  ('openpyxl.chart._chart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.chartspace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-2'),
  ('openpyxl.chart.print_settings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-2'),
  ('openpyxl.chart.plotarea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.image',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-2'),
  ('openpyxl.utils.units',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-2'),
  ('openpyxl.styles.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-2'),
  ('openpyxl.styles.colors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.styles.protection',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fonts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fills',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-2'),
  ('openpyxl.styles.borders',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-2'),
  ('openpyxl.styles.alignment',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-2'),
  ('openpyxl.styles.differential',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-2'),
  ('openpyxl.styles.named_styles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-2'),
  ('openpyxl.styles.cell_style',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-2'),
  ('openpyxl.writer.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.extended',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-2'),
  ('openpyxl.writer.theme',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-2'),
  ('openpyxl.workbook._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.web',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.smart_tags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.function_group',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_reference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.filters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.related',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.merge',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.cell_range',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-2'),
  ('openpyxl.cell.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.cell',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.cell.read_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-2'),
  ('openpyxl.cell.rich_text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-2'),
  ('openpyxl.cell.text',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.formula',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-2'),
  ('openpyxl.styles.styleable',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-2'),
  ('openpyxl.styles.builtins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-2'),
  ('openpyxl.styles.proxy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.hyperlink',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.dimensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-2'),
  ('openpyxl.utils.bound_dictionary',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-2'),
  ('openpyxl.styles.stylesheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comment_sheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-2'),
  ('openpyxl.comments',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.comments.shape_writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comments',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-2'),
  ('openpyxl.comments.author',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.manifest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-2'),
  ('mimetypes', 'C:\\Python313\\Lib\\mimetypes.py', 'PYMODULE-2'),
  ('openpyxl.utils.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-2'),
  ('openpyxl.utils.datetime',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-2'),
  ('openpyxl.utils.indexed_list',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-2'),
  ('openpyxl.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.utils.formulas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.copier',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._write_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._read_only',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.properties',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.datavalidation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.views',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.scenario',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.pagebreak',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-2'),
  ('openpyxl.formula.translate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.formatting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-2'),
  ('openpyxl.formatting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.rule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.worksheet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.print_settings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.compat.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.compat.numbers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl._constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-2'),
  ('openpyxl.reader.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.reader.drawings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-2'),
  ('openpyxl.reader.workbook',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.record',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-2'),
  ('openpyxl.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.fields',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.cache',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-2'),
  ('openpyxl.reader.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.table',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-2'),
  ('openpyxl.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.xml.functions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-2'),
  ('et_xmlfile.xmlfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-2'),
  ('et_xmlfile.incremental_tree',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-2'),
  ('et_xmlfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.xml.constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.relationship',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.container',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.sequence',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.namespace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.nested',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.serialisable',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-2'),
  ('openpyxl.cell._writer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-2'),
  ('geopy.geocoders',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\__init__.py',
   'PYMODULE-2'),
  ('geopy.geocoders.yandex',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\yandex.py',
   'PYMODULE-2'),
  ('geopy.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\util.py',
   'PYMODULE-2'),
  ('geopy.location',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\location.py',
   'PYMODULE-2'),
  ('geopy.point',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\point.py',
   'PYMODULE-2'),
  ('geopy.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\format.py',
   'PYMODULE-2'),
  ('geopy.units',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\units.py',
   'PYMODULE-2'),
  ('geopy.geocoders.woosmap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\woosmap.py',
   'PYMODULE-2'),
  ('geopy.geocoders.what3words',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\what3words.py',
   'PYMODULE-2'),
  ('geopy.geocoders.tomtom',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\tomtom.py',
   'PYMODULE-2'),
  ('geopy.adapters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\adapters.py',
   'PYMODULE-2'),
  ('asyncio', 'C:\\Python313\\Lib\\asyncio\\__init__.py', 'PYMODULE-2'),
  ('asyncio.unix_events',
   'C:\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.log', 'C:\\Python313\\Lib\\asyncio\\log.py', 'PYMODULE-2'),
  ('asyncio.windows_events',
   'C:\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'C:\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'C:\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'C:\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'C:\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.threads', 'C:\\Python313\\Lib\\asyncio\\threads.py', 'PYMODULE-2'),
  ('asyncio.taskgroups',
   'C:\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'C:\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.streams', 'C:\\Python313\\Lib\\asyncio\\streams.py', 'PYMODULE-2'),
  ('asyncio.runners', 'C:\\Python313\\Lib\\asyncio\\runners.py', 'PYMODULE-2'),
  ('asyncio.base_events',
   'C:\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.trsock', 'C:\\Python313\\Lib\\asyncio\\trsock.py', 'PYMODULE-2'),
  ('asyncio.staggered',
   'C:\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.timeouts',
   'C:\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-2'),
  ('asyncio.tasks', 'C:\\Python313\\Lib\\asyncio\\tasks.py', 'PYMODULE-2'),
  ('asyncio.queues', 'C:\\Python313\\Lib\\asyncio\\queues.py', 'PYMODULE-2'),
  ('asyncio.base_tasks',
   'C:\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.locks', 'C:\\Python313\\Lib\\asyncio\\locks.py', 'PYMODULE-2'),
  ('asyncio.mixins', 'C:\\Python313\\Lib\\asyncio\\mixins.py', 'PYMODULE-2'),
  ('asyncio.sslproto',
   'C:\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'C:\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'C:\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.futures', 'C:\\Python313\\Lib\\asyncio\\futures.py', 'PYMODULE-2'),
  ('asyncio.base_futures',
   'C:\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'C:\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.events', 'C:\\Python313\\Lib\\asyncio\\events.py', 'PYMODULE-2'),
  ('asyncio.format_helpers',
   'C:\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'C:\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'C:\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('geopy.geocoders.smartystreets',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\smartystreets.py',
   'PYMODULE-2'),
  ('geopy.geocoders.pickpoint',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\pickpoint.py',
   'PYMODULE-2'),
  ('geopy.geocoders.photon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\photon.py',
   'PYMODULE-2'),
  ('geopy.geocoders.pelias',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\pelias.py',
   'PYMODULE-2'),
  ('geopy.geocoders.openmapquest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\openmapquest.py',
   'PYMODULE-2'),
  ('geopy.geocoders.opencage',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\opencage.py',
   'PYMODULE-2'),
  ('geopy.geocoders.nominatim',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\nominatim.py',
   'PYMODULE-2'),
  ('geopy.geocoders.maptiler',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\maptiler.py',
   'PYMODULE-2'),
  ('geopy.geocoders.mapquest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\mapquest.py',
   'PYMODULE-2'),
  ('geopy.geocoders.mapbox',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\mapbox.py',
   'PYMODULE-2'),
  ('geopy.geocoders.ignfrance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\ignfrance.py',
   'PYMODULE-2'),
  ('geopy.geocoders.here',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\here.py',
   'PYMODULE-2'),
  ('geopy.geocoders.google',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\google.py',
   'PYMODULE-2'),
  ('geopy.timezone',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\timezone.py',
   'PYMODULE-2'),
  ('pytz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-2'),
  ('pytz.tzfile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-2'),
  ('pytz.tzinfo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-2'),
  ('pytz.lazy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-2'),
  ('pytz.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geonames',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geonames.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geolake',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geolake.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geokeo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geokeo.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geocodio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geocodio.py',
   'PYMODULE-2'),
  ('geopy.geocoders.geocodeearth',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\geocodeearth.py',
   'PYMODULE-2'),
  ('geopy.geocoders.databc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\databc.py',
   'PYMODULE-2'),
  ('geopy.geocoders.bing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\bing.py',
   'PYMODULE-2'),
  ('geopy.geocoders.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\base.py',
   'PYMODULE-2'),
  ('geopy.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\compat.py',
   'PYMODULE-2'),
  ('geopy.geocoders.banfrance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\banfrance.py',
   'PYMODULE-2'),
  ('geopy.geocoders.baidu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\baidu.py',
   'PYMODULE-2'),
  ('geopy.geocoders.azure',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\azure.py',
   'PYMODULE-2'),
  ('geopy.geocoders.arcgis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\geocoders\\arcgis.py',
   'PYMODULE-2'),
  ('geopy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\__init__.py',
   'PYMODULE-2'),
  ('geopy.exc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\exc.py',
   'PYMODULE-2'),
  ('pandas._libs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.tslibs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-2'),
  ('pandas.tseries.holiday',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-2'),
  ('pandas.tseries',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-2'),
  ('pandas.tseries.frequencies',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-2'),
  ('pandas.core.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-2'),
  ('pandas.core.tools.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.tools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.ops.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-2'),
  ('pandas.core.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.ops.mask_ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.invalid',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-2'),
  ('pandas.core.ops.docstrings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-2'),
  ('pandas.core.ops.array_ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.dispatch',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expressions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-2'),
  ('pandas.core.computation.check',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-2'),
  ('pandas.compat._optional',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-2'),
  ('pandas.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat.pyarrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-2'),
  ('pandas.compat.compressors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-2'),
  ('pandas.compat._constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-2'),
  ('pandas.util.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-2'),
  ('pandas.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.util.hashing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-2'),
  ('pandas.core.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config.display',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-2'),
  ('pandas._config.dates',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-2'),
  ('pandas._config.config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-2'),
  ('pandas.core.computation',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-2'),
  ('pandas.io.formats.printing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-2'),
  ('pandas.io.formats.console',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.inference',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-2'),
  ('pandas.io.formats.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-2'),
  ('pandas.io.excel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlsxwriter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.excel._openpyxl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odswriter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlrd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-2'),
  ('pandas.io.excel._pyxlsb',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odfreader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-2'),
  ('pandas.io.excel._calamine',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.readers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.python_parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.c_parser_wrapper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.categorical',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.grouper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.categorical',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.reshape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.strings.object_array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-2'),
  ('pandas.core.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.strings.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-2'),
  ('pandas.core.sorting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-2'),
  ('pandas.core.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.indexers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.transforms',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.quantile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-2'),
  ('pandas.core.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.arraylike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.astype',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.base_parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io._util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.parsers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-2'),
  ('pandas.io.formats.css',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-2'),
  ('pandas.io.formats._color_data',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style_render',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-2'),
  ('pandas.api.types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.stata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.range',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-2'),
  ('pandas.core.indexes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-2'),
  ('pandas.io.json._json',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-2'),
  ('pandas.io.json',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.json._table_schema',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.multi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.util',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.frozen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.putmask',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-2'),
  ('pandas.io.json._normalize',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-2'),
  ('pandas.core.window',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.window.expanding',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-2'),
  ('pandas.core.window.doc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.objects',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-2'),
  ('pandas._libs.window',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.window.ewm',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-2'),
  ('pandas.core.window.online',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-2'),
  ('pandas.core.window.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-2'),
  ('pandas.core.util.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-2'),
  ('pandas.api.interchange',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.from_dataframe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe_protocol',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-2'),
  ('pandas.api.indexers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-2'),
  ('uuid', 'C:\\Python313\\Lib\\uuid.py', 'PYMODULE-2'),
  ('pandas.core.shared_docs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-2'),
  ('pandas.io',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.gbq',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-2'),
  ('pandas.io.clipboards',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-2'),
  ('pandas.io.clipboard',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.sql',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-2'),
  ('sqlite3', 'C:\\Python313\\Lib\\sqlite3\\__init__.py', 'PYMODULE-2'),
  ('sqlite3.dump', 'C:\\Python313\\Lib\\sqlite3\\dump.py', 'PYMODULE-2'),
  ('sqlite3.__main__',
   'C:\\Python313\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2', 'C:\\Python313\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE-2'),
  ('pandas.core.internals.construction',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.internals.managers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-2'),
  ('pandas.core.internals.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.internals.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-2'),
  ('pandas.core.internals.blocks',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.replace',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-2'),
  ('pandas.core.internals.array_manager',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.take',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy.testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.testing._private.utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-2'),
  ('difflib', 'C:\\Python313\\Lib\\difflib.py', 'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._format_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.recfunctions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy.version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('numpy.testing._private.extbuild',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-2'),
  ('numpy.testing.overrides',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-2'),
  ('numpy.testing._private',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('pandas.io.pytables',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-2'),
  ('pandas.core.computation.pytables',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-2'),
  ('pandas.core.computation.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-2'),
  ('pandas.compat.pickle_compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-2'),
  ('pandas.core.computation.scope',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-2'),
  ('pandas.core.computation.parsing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-2'),
  ('pandas.core.computation.ops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.computation.eval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-2'),
  ('pandas.core.computation.engines',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-2'),
  ('pandas.core.computation.align',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.cast',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-2'),
  ('pandas.core.tools.times',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.masked',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_reductions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_accumulations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.accessors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._arrow_string_mixins',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.ops.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._ranges',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.roperator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-2'),
  ('pandas.core.nanops',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-2'),
  ('pandas.util._validators',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy.function',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('pandas.util._exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-2'),
  ('pandas.core.tools.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.tools.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.indexing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.timedeltas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.extension',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimelike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.period',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.period',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.extension_types',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-2'),
  ('pickletools', 'C:\\Python313\\Lib\\pickletools.py', 'PYMODULE-2'),
  ('pandas.core.arrays.interval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.interval',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-2'),
  ('pandas.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.sample',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-2'),
  ('dateutil.parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.parser.isoparser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-2'),
  ('six',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\six.py',
   'PYMODULE-2'),
  ('dateutil.tz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.tz.tz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-2'),
  ('dateutil.zoneinfo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.rrule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-2'),
  ('dateutil.easter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-2'),
  ('dateutil._common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-2'),
  ('dateutil.tz.win',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-2'),
  ('dateutil.tz._factories',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-2'),
  ('dateutil.tz._common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-2'),
  ('dateutil.parser._parser',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-2'),
  ('dateutil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-2'),
  ('dateutil._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.category',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-2'),
  ('pandas.core.groupby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.groupby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.var_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.shared',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.sum_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.min_max_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.mean_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.indexing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_arrow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numeric',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-2'),
  ('pandas.core.apply',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-2'),
  ('pandas.core._numba.extensions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-2'),
  ('pandas.core._numba.executor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-2'),
  ('pandas.core._numba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.numba_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-2'),
  ('pandas.core.flags',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-2'),
  ('pandas.core.construction',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numpy_',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.integer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.floating',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.boolean',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-2'),
  ('pandas.core.arrays',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.array',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.missing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimelike',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.algorithms',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.tile',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.common',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-2'),
  ('pandas.util._decorators',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-2'),
  ('pandas.tseries.offsets',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-2'),
  ('pandas.errors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.relativedelta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-2'),
  ('pandas.io.formats.format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-2'),
  ('pandas.io.formats.csvs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-2'),
  ('pandas.io.formats.string',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-2'),
  ('pandas.io.formats.html',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-2'),
  ('pandas.core.window.rolling',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-2'),
  ('pandas.core.series',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.reshape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-2'),
  ('pandas.plotting',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-2'),
  ('pandas.plotting._misc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-2'),
  ('pandas.plotting._core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-2'),
  ('pandas.io.formats.info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-2'),
  ('pandas.core.strings.accessor',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.methods.selectn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-2'),
  ('pandas.core.methods',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.accessors',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.resample',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-2'),
  ('pandas.core.internals',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.internals.concat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.internals.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.merge',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.generic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-2'),
  ('pandas.io.pickle',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-2'),
  ('pandas.core.methods.describe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-2'),
  ('pandas.core.frame',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.pivot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-2'),
  ('pandas.io.formats.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-2'),
  ('xml.dom.minidom', 'C:\\Python313\\Lib\\xml\\dom\\minidom.py', 'PYMODULE-2'),
  ('xml.dom.pulldom', 'C:\\Python313\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE-2'),
  ('xml.dom.expatbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.NodeFilter',
   'C:\\Python313\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-2'),
  ('xml.dom.xmlbuilder',
   'C:\\Python313\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.minicompat',
   'C:\\Python313\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE-2'),
  ('xml.dom.domreg', 'C:\\Python313\\Lib\\xml\\dom\\domreg.py', 'PYMODULE-2'),
  ('xml.dom', 'C:\\Python313\\Lib\\xml\\dom\\__init__.py', 'PYMODULE-2'),
  ('pandas.io.xml',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-2'),
  ('pandas.io.orc',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-2'),
  ('pandas.io.parquet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-2'),
  ('pandas.io.feather_format',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-2'),
  ('pandas.core.methods.to_dict',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.column',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.buffer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.melt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.base',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-2'),
  ('pandas.arrays',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('urllib.error', 'C:\\Python313\\Lib\\urllib\\error.py', 'PYMODULE-2'),
  ('urllib.response', 'C:\\Python313\\Lib\\urllib\\response.py', 'PYMODULE-2'),
  ('urllib.parse', 'C:\\Python313\\Lib\\urllib\\parse.py', 'PYMODULE-2'),
  ('ipaddress', 'C:\\Python313\\Lib\\ipaddress.py', 'PYMODULE-2'),
  ('urllib.request', 'C:\\Python313\\Lib\\urllib\\request.py', 'PYMODULE-2'),
  ('getpass', 'C:\\Python313\\Lib\\getpass.py', 'PYMODULE-2'),
  ('nturl2path', 'C:\\Python313\\Lib\\nturl2path.py', 'PYMODULE-2'),
  ('ftplib', 'C:\\Python313\\Lib\\ftplib.py', 'PYMODULE-2'),
  ('netrc', 'C:\\Python313\\Lib\\netrc.py', 'PYMODULE-2'),
  ('http.cookiejar', 'C:\\Python313\\Lib\\http\\cookiejar.py', 'PYMODULE-2'),
  ('urllib', 'C:\\Python313\\Lib\\urllib\\__init__.py', 'PYMODULE-2'),
  ('http.cookies', 'C:\\Python313\\Lib\\http\\cookies.py', 'PYMODULE-2'),
  ('http.server', 'C:\\Python313\\Lib\\http\\server.py', 'PYMODULE-2'),
  ('socketserver', 'C:\\Python313\\Lib\\socketserver.py', 'PYMODULE-2'),
  ('html', 'C:\\Python313\\Lib\\html\\__init__.py', 'PYMODULE-2'),
  ('html.entities', 'C:\\Python313\\Lib\\html\\entities.py', 'PYMODULE-2'),
  ('http.client', 'C:\\Python313\\Lib\\http\\client.py', 'PYMODULE-2'),
  ('http', 'C:\\Python313\\Lib\\http\\__init__.py', 'PYMODULE-2'),
  ('tracemalloc', 'C:\\Python313\\Lib\\tracemalloc.py', 'PYMODULE-2'),
  ('stringprep', 'C:\\Python313\\Lib\\stringprep.py', 'PYMODULE-2'),
  ('_py_abc', 'C:\\Python313\\Lib\\_py_abc.py', 'PYMODULE-2'),
  ('ctypes', 'C:\\Python313\\Lib\\ctypes\\__init__.py', 'PYMODULE-2'),
  ('ctypes._endian', 'C:\\Python313\\Lib\\ctypes\\_endian.py', 'PYMODULE-2'),
  ('subprocess', 'C:\\Python313\\Lib\\subprocess.py', 'PYMODULE-2'),
  ('threading', 'C:\\Python313\\Lib\\threading.py', 'PYMODULE-2'),
  ('_threading_local', 'C:\\Python313\\Lib\\_threading_local.py', 'PYMODULE-2'),
  ('pathlib', 'C:\\Python313\\Lib\\pathlib\\__init__.py', 'PYMODULE-2'),
  ('pathlib._local', 'C:\\Python313\\Lib\\pathlib\\_local.py', 'PYMODULE-2'),
  ('pathlib._abc', 'C:\\Python313\\Lib\\pathlib\\_abc.py', 'PYMODULE-2'),
  ('logging', 'C:\\Python313\\Lib\\logging\\__init__.py', 'PYMODULE-2'),
  ('typing', 'C:\\Python313\\Lib\\typing.py', 'PYMODULE-2'),
  ('multiprocessing',
   'C:\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('geopy.distance',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geopy\\distance.py',
   'PYMODULE-2'),
  ('geographiclib.geodesic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesic.py',
   'PYMODULE-2'),
  ('geographiclib.polygonarea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\polygonarea.py',
   'PYMODULE-2'),
  ('geographiclib.accumulator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\accumulator.py',
   'PYMODULE-2'),
  ('geographiclib.geodesicline',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesicline.py',
   'PYMODULE-2'),
  ('geographiclib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\__init__.py',
   'PYMODULE-2'),
  ('geographiclib.geodesiccapability',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geodesiccapability.py',
   'PYMODULE-2'),
  ('geographiclib.constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\constants.py',
   'PYMODULE-2'),
  ('geographiclib.geomath',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\geographiclib\\geomath.py',
   'PYMODULE-2'),
  ('tkinter.messagebox',
   'C:\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE-2'),
  ('tkinter.commondialog',
   'C:\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE-2'),
  ('tkinter.ttk', 'C:\\Python313\\Lib\\tkinter\\ttk.py', 'PYMODULE-2'),
  ('tkinter.filedialog',
   'C:\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE-2'),
  ('tkinter.simpledialog',
   'C:\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE-2'),
  ('tkinter.dialog', 'C:\\Python313\\Lib\\tkinter\\dialog.py', 'PYMODULE-2'),
  ('tkinter', 'C:\\Python313\\Lib\\tkinter\\__init__.py', 'PYMODULE-2'),
  ('tkinter.constants',
   'C:\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE-2'),
  ('numpy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy.core',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.char',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py.diagnose',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-2'),
  ('numpy.f2py.f2py2e',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-2'),
  ('numpy.f2py.crackfortran',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-2'),
  ('fileinput', 'C:\\Python313\\Lib\\fileinput.py', 'PYMODULE-2'),
  ('numpy.f2py._backends',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._distutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._backend',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._meson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-2'),
  ('numpy.f2py.rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.use_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.f90mod_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.common_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.func2subr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-2'),
  ('numpy.f2py._isocbind',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-2'),
  ('numpy.f2py.auxfuncs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.symbolic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-2'),
  ('numpy.f2py.cb_rules',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.capi_maps',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-2'),
  ('numpy.f2py.cfuncs',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.__version__',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib._ctypeslib',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('pandas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas7bdat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sasreader',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_xport',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_constants',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-2'),
  ('pandas.io.sas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-2'),
  ('pandas._version',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-2'),
  ('pandas._version_meson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-2'),
  ('pandas.util._tester',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-2'),
  ('pandas.io.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-2'),
  ('pandas.io.spss',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-2'),
  ('pandas.io.html',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-2'),
  ('pandas.util._print_versions',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-2'),
  ('pandas.testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-2'),
  ('pandas._testing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing.contexts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-2'),
  ('pandas._testing.compat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-2'),
  ('pandas._testing.asserters',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-2'),
  ('pandas._testing._warnings',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-2'),
  ('pandas._testing._io',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-2'),
  ('pandas._config.localization',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.encoding',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-2'),
  ('pandas.core.computation.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-2'),
  ('pandas.tseries.api',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-2'),
  ('pandas.core.config_init',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-2')],
 [('python313.dll', 'C:\\Python313\\python313.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'C:\\Python313\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\Python313\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python313\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python313\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python313\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python313\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_elementtree.pyd', 'C:\\Python313\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python313\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python313\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\testing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\hashing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\groupby.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\arrays.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\parsers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\json.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python313\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\internals.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\indexing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\sparse.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\interval.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\join.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\reshape.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\ops.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\writers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\index.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\algos.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\missing.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\lib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\properties.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python313\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\sas.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Python313\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python313\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python313\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Python313\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\Python313\\DLLs\\tcl86t.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\System32\\ucrtbase.dll', 'BINARY'),
  ('zlib1.dll', 'C:\\Python313\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('icon.ico', 'E:\\cursor_project\\方便截图软件\\icon.ico', 'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Python313\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:\\Python313\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tk_data\\focus.tcl', 'C:\\Python313\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\history.tcl', 'C:\\Python313\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'C:\\Python313\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'C:\\Python313\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:\\Python313\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tk_data\\palette.tcl', 'C:\\Python313\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Python313\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\package.tcl', 'C:\\Python313\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Python313\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\EET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:\\Python313\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'C:\\Python313\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\init.tcl', 'C:\\Python313\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'C:\\Python313\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\word.tcl', 'C:\\Python313\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Python313\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\fi.msg', 'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Python313\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Python313\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tk_data\\text.tcl', 'C:\\Python313\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Python313\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python313\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'C:\\Python313\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'C:\\Python313\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'C:\\Python313\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'C:\\Python313\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl', 'C:\\Python313\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Python313\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'C:\\Python313\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tk_data\\menu.tcl', 'C:\\Python313\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'C:\\Python313\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tk_data\\entry.tcl', 'C:\\Python313\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tk_data\\iconlist.tcl', 'C:\\Python313\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'C:\\Python313\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'C:\\Python313\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'C:\\Python313\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tk_data\\button.tcl', 'C:\\Python313\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tk_data\\scale.tcl', 'C:\\Python313\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'C:\\Python313\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tk_data\\console.tcl', 'C:\\Python313\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\tkfbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tk_data\\tclIndex', 'C:\\Python313\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python313\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\tzdata\\MST', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Python313\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tk_data\\tk.tcl', 'C:\\Python313\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tk_data\\icons.tcl', 'C:\\Python313\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tk_data\\listbox.tcl', 'C:\\Python313\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'C:\\Python313\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'C:\\Python313\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'C:\\Python313\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Python313\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'C:\\Python313\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tclIndex', 'C:\\Python313\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Python313\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Python313\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Python313\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'C:\\Python313\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Python313\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('numpy-2.3.1.dist-info\\LICENSE.txt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\METADATA',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.1.dist-info\\RECORD',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.3.1.dist-info\\WHEEL',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info\\DELVEWHEEL',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\INSTALLER',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info\\entry_points.txt',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\REQUESTED',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'E:\\cursor_project\\方便截图软件\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'E:\\cursor_project\\方便截图软件\\build\\工地周边数据处理工具\\base_library.zip',
   'DATA')],
 [('codecs', 'C:\\Python313\\Lib\\codecs.py', 'PYMODULE'),
  ('collections', 'C:\\Python313\\Lib\\collections\\__init__.py', 'PYMODULE'),
  ('weakref', 'C:\\Python313\\Lib\\weakref.py', 'PYMODULE'),
  ('heapq', 'C:\\Python313\\Lib\\heapq.py', 'PYMODULE'),
  ('warnings', 'C:\\Python313\\Lib\\warnings.py', 'PYMODULE'),
  ('io', 'C:\\Python313\\Lib\\io.py', 'PYMODULE'),
  ('re._parser', 'C:\\Python313\\Lib\\re\\_parser.py', 'PYMODULE'),
  ('re._constants', 'C:\\Python313\\Lib\\re\\_constants.py', 'PYMODULE'),
  ('re._compiler', 'C:\\Python313\\Lib\\re\\_compiler.py', 'PYMODULE'),
  ('re._casefix', 'C:\\Python313\\Lib\\re\\_casefix.py', 'PYMODULE'),
  ('keyword', 'C:\\Python313\\Lib\\keyword.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('locale', 'C:\\Python313\\Lib\\locale.py', 'PYMODULE'),
  ('stat', 'C:\\Python313\\Lib\\stat.py', 'PYMODULE'),
  ('ntpath', 'C:\\Python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'C:\\Python313\\Lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'C:\\Python313\\Lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'C:\\Python313\\Lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'C:\\Python313\\Lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'C:\\Python313\\Lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'C:\\Python313\\Lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'C:\\Python313\\Lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'C:\\Python313\\Lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'C:\\Python313\\Lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'C:\\Python313\\Lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'C:\\Python313\\Lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'C:\\Python313\\Lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'C:\\Python313\\Lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'C:\\Python313\\Lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'C:\\Python313\\Lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'C:\\Python313\\Lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'C:\\Python313\\Lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'C:\\Python313\\Lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'C:\\Python313\\Lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'C:\\Python313\\Lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'C:\\Python313\\Lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'C:\\Python313\\Lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'C:\\Python313\\Lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'C:\\Python313\\Lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'C:\\Python313\\Lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'C:\\Python313\\Lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'C:\\Python313\\Lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'C:\\Python313\\Lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'C:\\Python313\\Lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'C:\\Python313\\Lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'C:\\Python313\\Lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'C:\\Python313\\Lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'C:\\Python313\\Lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'C:\\Python313\\Lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'C:\\Python313\\Lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'C:\\Python313\\Lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'C:\\Python313\\Lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'C:\\Python313\\Lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'C:\\Python313\\Lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'C:\\Python313\\Lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'C:\\Python313\\Lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'C:\\Python313\\Lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'C:\\Python313\\Lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'C:\\Python313\\Lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'C:\\Python313\\Lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'C:\\Python313\\Lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'C:\\Python313\\Lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'C:\\Python313\\Lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'C:\\Python313\\Lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'C:\\Python313\\Lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'C:\\Python313\\Lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'C:\\Python313\\Lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'C:\\Python313\\Lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'C:\\Python313\\Lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'C:\\Python313\\Lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'C:\\Python313\\Lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'C:\\Python313\\Lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'C:\\Python313\\Lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'C:\\Python313\\Lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'C:\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'C:\\Python313\\Lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'C:\\Python313\\Lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'C:\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'C:\\Python313\\Lib\\encodings\\__init__.py', 'PYMODULE'),
  ('copyreg', 'C:\\Python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('posixpath', 'C:\\Python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('sre_constants', 'C:\\Python313\\Lib\\sre_constants.py', 'PYMODULE'),
  ('enum', 'C:\\Python313\\Lib\\enum.py', 'PYMODULE'),
  ('genericpath', 'C:\\Python313\\Lib\\genericpath.py', 'PYMODULE'),
  ('functools', 'C:\\Python313\\Lib\\functools.py', 'PYMODULE'),
  ('_weakrefset', 'C:\\Python313\\Lib\\_weakrefset.py', 'PYMODULE'),
  ('traceback', 'C:\\Python313\\Lib\\traceback.py', 'PYMODULE'),
  ('linecache', 'C:\\Python313\\Lib\\linecache.py', 'PYMODULE'),
  ('types', 'C:\\Python313\\Lib\\types.py', 'PYMODULE'),
  ('operator', 'C:\\Python313\\Lib\\operator.py', 'PYMODULE'),
  ('abc', 'C:\\Python313\\Lib\\abc.py', 'PYMODULE'),
  ('reprlib', 'C:\\Python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('_collections_abc', 'C:\\Python313\\Lib\\_collections_abc.py', 'PYMODULE'),
  ('sre_compile', 'C:\\Python313\\Lib\\sre_compile.py', 'PYMODULE'),
  ('re', 'C:\\Python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('os', 'C:\\Python313\\Lib\\os.py', 'PYMODULE')])
